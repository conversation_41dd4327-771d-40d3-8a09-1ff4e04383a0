"""
多智能体协调器 - 负责协调各个智能体的工作流程
"""

import asyncio
import logging
import json
import re
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
import pandas as pd
import base64
import tempfile
import os
import zipfile
from io import BytesIO

from autogen_core.base import CancellationToken
from autogen_core.components import AssistantAgent
from autogen_core.components.models import ChatCompletionClient
from autogen_core.components.models._types import TextMessage

import docker
import os
import tempfile
import time

# 导入agents模块中的类
from agents import (
    DatabaseManager, ModelManager, SessionManager, ConversationManager,
    RelationshipVisualizationAgent, ReportGeneratorAgent, BatchCaseExtractionAgent
)

class ExcelFileProcessor:
    """Excel文件处理器"""
    
    def __init__(self):
        pass
    
    def process_excel_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """处理Excel文件并提取案件数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(BytesIO(file_content))
            
            # 检查必要的列
            required_columns = ['案件编号', '案件名称', '承办单位', '案件内容']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                return {
                    "status": "error",
                    "error": f"缺少必要的列: {', '.join(missing_columns)}"
                }
            
            # 转换为字典列表
            cases_data = []
            for index, row in df.iterrows():
                case_data = {
                    '案件编号': str(row.get('案件编号', '')),
                    '案件名称': str(row.get('案件名称', '')),
                    '承办单位': str(row.get('承办单位', '')),
                    '案件内容': str(row.get('案件内容', '')),
                    '录入时间': str(row.get('录入时间', datetime.now().strftime('%Y-%m-%d')))
                }
                cases_data.append(case_data)
            
            return {
                "status": "success",
                "cases_data": cases_data,
                "total_cases": len(cases_data),
                "filename": filename
            }
            
        except Exception as e:
            logging.error(f"Excel文件处理失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

class BatchDatabaseInsertAgent:
    """批量数据库插入智能体"""

    def __init__(self, db_manager: DatabaseManager, session_manager: SessionManager):
        self.db_manager = db_manager
        self.session_manager = session_manager

    async def insert_batch_data(self, csv_data: str, batch_id: str, session_id: str) -> Dict[str, Any]:
        """批量插入CSV数据到数据库"""
        try:
            if not csv_data.strip():
                return {
                    "status": "error",
                    "error": "没有数据需要插入"
                }

            # 解析CSV数据
            lines = csv_data.strip().split('\n')
            if len(lines) < 2:
                return {
                    "status": "error",
                    "error": "CSV数据格式不正确"
                }

            header = lines[0].split(',')
            data_rows = []

            for line in lines[1:]:
                if line.strip():
                    row_data = line.split(',')
                    if len(row_data) == len(header):
                        data_rows.append(dict(zip(header, row_data)))

            if not data_rows:
                return {
                    "status": "error",
                    "error": "没有有效的数据行"
                }

            # 插入数据库
            success_count = 0
            failed_count = 0
            errors = []

            for row in data_rows:
                try:
                    # 这里应该调用实际的数据库插入方法
                    # self.db_manager.insert_case_data(row)
                    success_count += 1
                except Exception as e:
                    failed_count += 1
                    errors.append(str(e))

            return {
                "status": "success",
                "total_rows": len(data_rows),
                "success_count": success_count,
                "failed_count": failed_count,
                "errors": errors,
                "batch_id": batch_id
            }

        except Exception as e:
            logging.error(f"批量数据库插入失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

class MultiCaseAnalysisOrchestrator:
    """多案件分析协调器 - 协调整个分析流程"""

    def __init__(self, max_concurrent: int = 10, max_repair_attempts: int = 2):
        self.session_manager = SessionManager()
        self.model_manager = ModelManager()
        self.db_manager = DatabaseManager()
        self.file_processor = ExcelFileProcessor()
        self.batch_extractor = BatchCaseExtractionAgent(self.model_manager, self.session_manager, max_concurrent, max_repair_attempts)
        self.batch_db_inserter = BatchDatabaseInsertAgent(self.db_manager, self.session_manager)
        self.relationship_visualizer = RelationshipVisualizationAgent(self.session_manager, self.model_manager, max_repair_attempts)
        self.report_generator = ReportGeneratorAgent(self.session_manager)

    async def process_excel_file(self, file_content: bytes, filename: str, 
                               user_requirements: str = None, progress_callback=None) -> Dict[str, Any]:
        """处理Excel文件的完整流程"""
        try:
            # 创建新的会话
            session_id = self.session_manager.create_session()
            batch_id = str(uuid.uuid4())[:8]

            # 第一步：处理Excel文件
            if progress_callback:
                await progress_callback("file_processing", "processing", "正在处理Excel文件...")

            file_result = self.file_processor.process_excel_file(file_content, filename)
            if file_result["status"] != "success":
                return file_result

            cases_data = file_result["cases_data"]
            total_cases = len(cases_data)

            # 第二步：批量提取案件信息
            if progress_callback:
                await progress_callback("extraction", "processing", f"开始提取 {total_cases} 个案件的信息...")

            extraction_result = await self.batch_extractor.extract_multiple_cases(
                cases_data, batch_id, session_id, user_requirements, progress_callback
            )

            if extraction_result["status"] != "success":
                return extraction_result

            # 第三步：生成关系图
            if progress_callback:
                await progress_callback("visualization", "processing", "正在生成关系图...")

            visualization_results = {}
            mermaid_codes = extraction_result.get("mermaid_codes", {})

            for case_id, mermaid_code in mermaid_codes.items():
                if mermaid_code and case_id:
                    visualization_result = await self.relationship_visualizer.render_mermaid_to_image(
                        mermaid_code, session_id, case_id
                    )
                    if visualization_result.get("status") == "success":
                        visualization_results[case_id] = visualization_result

            # 第四步：保存数据到数据库（可选）
            if progress_callback:
                await progress_callback("database", "processing", "正在保存数据到数据库...")

            db_result = await self.batch_db_inserter.insert_batch_data(
                extraction_result.get("merged_csv_data", ""), batch_id, session_id
            )

            # 第五步：生成报告
            if progress_callback:
                await progress_callback("report", "processing", "正在生成分析报告...")

            report_result = await self.report_generator.generate_batch_report(
                extraction_result, visualization_results, session_id, batch_id
            )

            # 完成
            if progress_callback:
                await progress_callback("completed", "completed", "处理完成！")

            return {
                "status": "success",
                "session_id": session_id,
                "batch_id": batch_id,
                "total_cases": total_cases,
                "successful_count": extraction_result.get("successful_count", 0),
                "failed_count": extraction_result.get("failed_count", 0),
                "extraction_result": extraction_result,
                "visualization_results": visualization_results,
                "database_result": db_result,
                "report_result": report_result,
                "processing_summary": extraction_result.get("processing_summary", "")
            }

        except Exception as e:
            logging.error(f"多案件分析流程失败: {e}")
            if progress_callback:
                await progress_callback("error", "failed", f"处理失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }

    def get_session_results(self, session_id: str) -> Dict[str, Any]:
        """获取会话结果"""
        try:
            session_dir = self.session_manager.get_session_dir(session_id)

            # 读取输出文件
            outputs_dir = session_dir / "outputs"
            if not outputs_dir.exists():
                return {
                    "status": "error",
                    "error": "会话输出目录不存在"
                }

            # 收集所有输出文件
            csv_files = list(outputs_dir.glob("*.csv"))
            image_files = list(outputs_dir.glob("*.png"))
            report_files = list(outputs_dir.glob("*.html"))

            return {
                "status": "success",
                "session_id": session_id,
                "csv_files": [str(f) for f in csv_files],
                "image_files": [str(f) for f in image_files],
                "report_files": [str(f) for f in report_files],
                "outputs_dir": str(outputs_dir)
            }

        except Exception as e:
            logging.error(f"获取会话结果失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def create_download_package(self, session_id: str) -> Dict[str, Any]:
        """创建下载包"""
        try:
            session_results = self.get_session_results(session_id)
            if session_results["status"] != "success":
                return session_results

            # 创建ZIP文件
            zip_buffer = BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # 添加CSV文件
                for csv_file in session_results["csv_files"]:
                    zip_file.write(csv_file, f"csv/{Path(csv_file).name}")

                # 添加图片文件
                for image_file in session_results["image_files"]:
                    zip_file.write(image_file, f"images/{Path(image_file).name}")

                # 添加报告文件
                for report_file in session_results["report_files"]:
                    zip_file.write(report_file, f"reports/{Path(report_file).name}")

            zip_buffer.seek(0)
            zip_data = zip_buffer.getvalue()

            return {
                "status": "success",
                "zip_data": zip_data,
                "filename": f"analysis_results_{session_id}.zip"
            }

        except Exception as e:
            logging.error(f"创建下载包失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
