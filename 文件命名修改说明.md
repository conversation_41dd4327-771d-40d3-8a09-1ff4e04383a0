# 文件命名修改说明 - 使用案件编号命名

## 🎯 修改目标

将data路径下生成的 `diagram.mmd` 和 `diagram.png` 文件名改为使用案件编号命名，例如：
- `A4416235000002023126011.mmd`
- `A4416235000002023126011.png`

## ✅ 修改内容

### 一. 修改文件命名逻辑

#### 1. 修改 `render_mermaid_to_image` 方法中的文件名生成

**修改位置**: `agents.py` 第1223-1232行

**修改前**:
```python
session_dir = self.session_manager.get_session_dir(session_id)

filename = f"{case_name}_relationship_{int(datetime.now().timestamp())}.png"
output_path = session_dir / "outputs" / filename
```

**修改后**:
```python
session_dir = self.session_manager.get_session_dir(session_id)

# 使用案件编号作为文件名，如果case_name包含案件编号则直接使用，否则使用默认格式
if case_name and case_name != "relationship":
    base_filename = case_name
else:
    base_filename = f"relationship_{int(datetime.now().timestamp())}"

filename = f"{base_filename}.png"
output_path = session_dir / "outputs" / filename
```

#### 2. 修改第一个Docker生成方法

**修改位置**: `agents.py` 第1015-1035行

**修改前**:
```python
# 在session/data目录下创建mermaid文件
mmd_file = work_dir_abs / "diagram.mmd"
png_file = work_dir_abs / "diagram.png"
```

**修改后**:
```python
# 从输出路径中提取案件编号作为文件名
output_filename = Path(output_path).stem  # 获取不带扩展名的文件名

# 在session/data目录下创建mermaid文件，使用案件编号命名
mmd_file = work_dir_abs / f"{output_filename}.mmd"
png_file = work_dir_abs / f"{output_filename}.png"
```

#### 3. 修改第一个Docker方法中的命令

**修改位置**: `agents.py` 第1072-1082行

**修改前**:
```python
command=[
    'mmdc',
    '-i', '/data/diagram.mmd',
    '-o', '/data/diagram.png',
    '--theme', 'default',
    '--scale', '2',
    '--backgroundColor', 'white'
],
```

**修改后**:
```python
command=[
    'mmdc',
    '-i', f'/data/{output_filename}.mmd',
    '-o', f'/data/{output_filename}.png',
    '--theme', 'default',
    '--scale', '2',
    '--backgroundColor', 'white'
],
```

#### 4. 修改第二个Docker生成方法

**修改位置**: `agents.py` 第1144-1152行

**修改前**:
```python
# 在session/data目录下创建mermaid文件
mmd_file = work_dir_abs / "diagram.mmd"
png_file = work_dir_abs / "diagram.png"
```

**修改后**:
```python
# 从输出路径中提取案件编号作为文件名
output_filename = Path(output_path).stem  # 获取不带扩展名的文件名

# 在session/data目录下创建mermaid文件，使用案件编号命名
mmd_file = work_dir_abs / f"{output_filename}.mmd"
png_file = work_dir_abs / f"{output_filename}.png"
```

#### 5. 修改第二个Docker方法中的命令

**修改位置**: `agents.py` 第1163-1174行

**修改前**:
```python
cmd = [
    'docker', 'run', '--rm',
    '-v', f'{work_dir_abs}:/data',
    '-w', '/data',
    'minlag/mermaid-cli:latest',
    'mmdc',
    '-i', '/data/diagram.mmd',
    '-o', '/data/diagram.png',
    '--theme', 'default',
    '--scale', '2',
    '--backgroundColor', 'white'
]
```

**修改后**:
```python
cmd = [
    'docker', 'run', '--rm',
    '-v', f'{work_dir_abs}:/data',
    '-w', '/data',
    'minlag/mermaid-cli:latest',
    'mmdc',
    '-i', f'/data/{output_filename}.mmd',
    '-o', f'/data/{output_filename}.png',
    '--theme', 'default',
    '--scale', '2',
    '--backgroundColor', 'white'
]
```

## 🔧 技术实现

### 文件名提取逻辑
```python
# 从输出路径中提取案件编号作为文件名
output_filename = Path(output_path).stem  # 获取不带扩展名的文件名
```

这个方法会从完整的输出路径中提取文件名（不包含扩展名），例如：
- 输入路径: `/sessions/abc123/outputs/A4416235000002023126011.png`
- 提取结果: `A4416235000002023126011`

### 动态文件命名
```python
# 使用案件编号作为文件名
if case_name and case_name != "relationship":
    base_filename = case_name  # 直接使用案件编号
else:
    base_filename = f"relationship_{int(datetime.now().timestamp())}"  # 默认格式
```

### Docker命令中的动态文件名
```python
# 在Docker命令中使用动态文件名
'-i', f'/data/{output_filename}.mmd',
'-o', f'/data/{output_filename}.png',
```

## 📁 文件结构变化

### 修改前的文件结构
```
sessions/
└── session_id/
    ├── data/
    │   ├── diagram.mmd          # 固定文件名
    │   └── diagram.png          # 固定文件名
    └── outputs/
        └── case_name_relationship_timestamp.png
```

### 修改后的文件结构
```
sessions/
└── session_id/
    ├── data/
    │   ├── A4416235000002023126011.mmd    # 使用案件编号
    │   └── A4416235000002023126011.png    # 使用案件编号
    └── outputs/
        └── A4416235000002023126011.png    # 使用案件编号
```

## 🎯 修改效果

### 1. 文件命名规范化
- ✅ 使用案件编号作为文件名，便于识别和管理
- ✅ 去掉了时间戳，避免文件名过长
- ✅ 保持了文件名的一致性

### 2. 文件管理优化
- ✅ data目录和outputs目录中的文件名保持一致
- ✅ 便于根据案件编号快速定位相关文件
- ✅ 支持批量处理时的文件区分

### 3. 兼容性保证
- ✅ 保持了原有的功能逻辑不变
- ✅ 支持默认情况下的文件命名
- ✅ 向后兼容现有的文件处理流程

## 🧪 测试验证

### 测试场景
1. **单个案件处理**: 验证使用案件编号生成文件
2. **批量案件处理**: 验证多个案件的文件命名不冲突
3. **默认情况**: 验证没有案件编号时的默认命名
4. **文件一致性**: 验证data和outputs目录中的文件名一致

### 预期结果
- ✅ 生成的.mmd和.png文件使用案件编号命名
- ✅ 文件名格式: `{案件编号}.mmd` 和 `{案件编号}.png`
- ✅ 例如: `A4416235000002023126011.mmd`, `A4416235000002023126011.png`
- ✅ 文件功能正常，关系图生成无问题

## 📝 使用示例

### 案件编号示例
```
案件编号: A4416235000002023126011
生成文件:
- /sessions/abc123/data/A4416235000002023126011.mmd
- /sessions/abc123/data/A4416235000002023126011.png
- /sessions/abc123/outputs/A4416235000002023126011.png
```

### 多案件示例
```
案件1: A4416235000002023126011
案件2: A4416235000002023126012
案件3: A4416235000002023126013

生成文件:
- A4416235000002023126011.mmd/png
- A4416235000002023126012.mmd/png  
- A4416235000002023126013.mmd/png
```

## 🎉 总结

通过这次修改，实现了：

1. **规范化命名**: 使用案件编号作为文件名，提高文件管理效率
2. **一致性保证**: data和outputs目录中的文件名保持一致
3. **易于识别**: 文件名直接对应案件编号，便于查找和管理
4. **功能完整**: 保持了原有的所有功能，只是改变了文件命名方式

现在生成的Mermaid文件和图片文件都会使用案件编号进行命名，大大提升了文件管理的便利性！🚀
