#!/usr/bin/env python3
"""
清洁布局演示
"""

import streamlit as st

def main():
    st.set_page_config(
        page_title="清洁布局演示",
        page_icon="✨",
        layout="wide"
    )
    
    st.title("✨ 清洁布局演示")
    st.write("展示移除重复标题后的清洁界面")
    
    # 模拟修改后的布局
    st.markdown("## 🎯 修改后的界面")
    
    # 分析需求区域（直接显示，无上方标题）
    st.markdown("### 📋 分析需求")
    
    default_analysis_requirements = """任务目标：
1. 组织架构解析（主犯/从犯认定、分工逻辑）
2. 结构化数据提取（CSV格式，14列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的组织和所有提及到的人物信息：实体类型（人员或组织等），姓名/代号，性别，年龄，身份证号，户籍地/现居地，文化程度，直接上级，所属组织，组织层级，分工角色,关联工具/行为,司法处置结果,经济收益（元）

"年龄"要素提取要求：
其中"年龄" 要素，优先从案件内容中分析获取，若无法直接获得"年龄"要素，则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄" 

输出CSV格式（14列）：
实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织之间的多层级关系，生成Mermaid格式的关系图代码。"""

    user_requirements = st.text_area(
        "",  # 无小字标签
        value=default_analysis_requirements,
        height=300,
        help="请输入具体的分析需求和任务目标，可以根据实际需要修改分析步骤、输出格式等"
    )
    
    # 文件上传区域
    st.markdown("---")
    st.markdown("### 📁 步骤1: 上传案件数据文件")
    uploaded_file = st.file_uploader(
        "",  # 无重复标题
        type=['xlsx', 'xls', 'csv'],
        help="支持Excel文件(.xlsx/.xls)和CSV文件(.csv)，必须包含案件编号字段"
    )
    
    # 对比说明
    st.markdown("---")
    st.markdown("## 📊 修改对比")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ❌ 修改前")
        st.markdown("""
        **问题：**
        - 有重复的标题
        - 分析需求被夹在中间
        - 视觉层次混乱
        - 用户可能困惑
        
        **结构：**
        ```
        📁 步骤1: 上传案件数据文件 (顶部)
        📋 分析需求
        [分析需求文本框]
        ---
        📁 步骤1: 上传案件数据文件 (重复)
        [文件上传器]
        ```
        """)
    
    with col2:
        st.markdown("### ✅ 修改后")
        st.markdown("""
        **优势：**
        - 无重复标题
        - 分析需求优先显示
        - 清晰的功能分区
        - 逻辑顺序合理
        
        **结构：**
        ```
        📋 分析需求
        [分析需求文本框]
        ---
        📁 步骤1: 上传案件数据文件
        [文件上传器]
        ```
        """)
    
    # 改进效果
    st.markdown("---")
    st.markdown("## ✨ 改进效果")
    
    improvements = [
        {
            "icon": "🎯",
            "title": "突出重点",
            "description": "分析需求作为第一个区域，更加突出重要性"
        },
        {
            "icon": "🧹",
            "title": "消除冗余",
            "description": "移除重复标题，界面更加简洁清晰"
        },
        {
            "icon": "📐",
            "title": "层次清晰",
            "description": "功能区域划分明确，视觉层次更好"
        },
        {
            "icon": "🔄",
            "title": "逻辑顺序",
            "description": "先配置需求，再上传文件，符合工作流程"
        },
        {
            "icon": "👁️",
            "title": "视觉舒适",
            "description": "减少视觉混乱，提升用户体验"
        },
        {
            "icon": "⚡",
            "title": "操作流畅",
            "description": "清晰的操作步骤，用户不会困惑"
        }
    ]
    
    # 显示改进效果
    for i in range(0, len(improvements), 2):
        cols = st.columns(2)
        for j, col in enumerate(cols):
            if i + j < len(improvements):
                improvement = improvements[i + j]
                with col:
                    st.markdown(f"""
                    **{improvement['icon']} {improvement['title']}**
                    
                    {improvement['description']}
                    """)
    
    # 技术细节
    st.markdown("---")
    st.markdown("## 🔧 技术实现")
    
    st.markdown("### 修改内容")
    st.code("""
# 移除的代码（第484行）
st.markdown('<div class="step-header">📁 步骤1: 上传案件数据文件</div>', unsafe_allow_html=True)

# 保留的结构
def display_file_upload_section():
    with st.container():
        # 分析需求区域（优先显示）
        st.markdown("### 📋 分析需求")
        user_requirements = st.text_area(...)
        
        # 文件上传区域
        st.markdown("---")
        st.markdown("### 📁 步骤1: 上传案件数据文件")
        uploaded_file = st.file_uploader(...)
""", language="python")
    
    # 用户指南
    st.markdown("---")
    st.markdown("## 📖 使用指南")
    
    st.markdown("""
    ### 🚀 操作流程
    
    1. **配置分析需求**
       - 查看默认的分析需求模板
       - 根据实际需要修改分析步骤
       - 调整输出格式和字段要求
    
    2. **上传案件文件**
       - 选择Excel或CSV格式的案件数据文件
       - 确保文件包含必要的案件编号字段
       - 系统会自动验证文件格式
    
    3. **开始分析**
       - 系统根据您配置的需求分析文件
       - 生成结构化的分析结果
       - 提供下载和查看功能
    
    ### 💡 界面特点
    
    - **清晰分区**: 分析需求和文件上传功能明确分离
    - **逻辑顺序**: 先配置需求，再上传文件
    - **无重复**: 消除了重复标题，界面更简洁
    - **易操作**: 清晰的步骤指引，操作更流畅
    """)

if __name__ == "__main__":
    main()
