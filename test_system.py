#!/usr/bin/env python3
"""
多案件信息提取分析助手 - 系统测试脚本
"""

import pandas as pd
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试用的Excel数据"""
    test_data = {
        '案件编号': [
            'A4413240420022025036001',
            'A4413240420022025036001', 
            'A4413240420022025036002',
            'A4413240420022025036003'
        ],
        '承办单位区域': ['广东', '广东', '广东', '广东'],
        '承办单位': ['深圳海关', '深圳海关', '广州海关', '珠海海关'],
        '承办单位名称': ['深圳海关缉私局', '深圳海关缉私局', '广州海关缉私局', '珠海海关缉私局'],
        '案件名称': ['走私香烟案', '走私香烟案', '走私电子产品案', '走私奢侈品案'],
        '正文内容': [
            '经查，犯罪嫌疑人张某某组织走私香烟团伙，通过海上偷运方式走私香烟入境。',
            '该团伙分工明确，张某某负责组织协调，李某某负责运输，王某某负责销售。',
            '犯罪嫌疑人赵某某利用职务便利，走私进口电子产品，涉案金额巨大。',
            '犯罪团伙以陈某某为首，专门从事奢侈品走私活动，手段隐蔽。'
        ],
        '到案情况': [
            '张某某已被抓获归案，李某某在逃。',
            '王某某主动投案自首。',
            '赵某某被海关查获。',
            '陈某某等三人全部到案。'
        ],
        '依法侦查查明': [
            '查明该团伙走私香烟价值500万元。',
            '查明涉案人员分工明确，组织严密。',
            '查明走私电子产品价值800万元。',
            '查明奢侈品走私网络遍布多个城市。'
        ],
        '犯罪证据': [
            '现场查获走私香烟1000箱，账本若干。',
            '银行转账记录，通话记录等。',
            '查获走私电子产品，相关单证。',
            '查获奢侈品，资金流水等证据。'
        ],
        '综上所述': [
            '张某某等人构成走私普通货物罪。',
            '建议依法严厉打击。',
            '赵某某构成走私普通货物罪。',
            '陈某某等人构成走私普通货物罪。'
        ],
        '其他说明': [
            '案件正在进一步侦办中。',
            '相关涉案人员仍在追捕。',
            '涉案资产已被查封。',
            '案件移送检察院审查起诉。'
        ],
        '数据版本号': [1, 2, 1, 1]
    }
    
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    test_file = Path("test_cases.xlsx")
    df.to_excel(test_file, index=False)
    
    logger.info(f"测试数据已保存到: {test_file}")
    return test_file

def test_file_processing():
    """测试文件处理功能"""
    try:
        from multi_agents import ExcelFileProcessor
        
        # 创建测试数据
        test_file = create_test_data()
        
        # 测试文件读取
        logger.info("测试文件读取...")
        df, file_info = ExcelFileProcessor.read_excel_file(str(test_file))
        
        logger.info(f"文件信息: {file_info}")
        logger.info(f"数据形状: {df.shape}")
        logger.info(f"列名: {df.columns.tolist()}")
        
        # 测试数据预处理
        logger.info("测试数据预处理...")
        preprocess_result = ExcelFileProcessor.preprocess_case_data(df)
        
        if preprocess_result["status"] == "success":
            logger.info(f"预处理成功: {preprocess_result['processing_summary']}")
            processed_df = preprocess_result["processed_data"]
            logger.info(f"处理后数据形状: {processed_df.shape}")
            logger.info(f"案件列表: {processed_df['案件编号'].tolist()}")
        else:
            logger.error(f"预处理失败: {preprocess_result.get('error')}")
        
        return True
        
    except Exception as e:
        logger.error(f"文件处理测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from multi_agents import DatabaseManager
        
        logger.info("测试数据库连接...")
        db_manager = DatabaseManager()
        
        # 测试连接
        connection = db_manager.get_connection()
        connection.close()
        
        logger.info("数据库连接测试成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False

def test_model_connection():
    """测试模型连接"""
    try:
        from multi_agents import ModelManager
        
        logger.info("测试模型连接...")
        model_manager = ModelManager()
        client = model_manager.get_client()
        
        logger.info("模型连接测试成功")
        return True
        
    except Exception as e:
        logger.error(f"模型连接测试失败: {e}")
        return False

async def test_case_extraction():
    """测试案件提取功能"""
    try:
        from multi_agents import BatchCaseExtractionAgent, ModelManager, SessionManager
        
        logger.info("测试案件提取功能...")
        
        # 创建测试组件
        model_manager = ModelManager()
        session_manager = SessionManager()
        extractor = BatchCaseExtractionAgent(model_manager, session_manager, max_concurrent=2)
        
        # 创建测试案件数据
        test_cases = [
            {
                '案件编号': 'TEST001',
                '案件名称': '测试走私案',
                '承办单位': '测试海关',
                '案件内容': '这是一个测试案件，涉及走私活动。犯罪嫌疑人张三组织走私团伙。'
            }
        ]
        
        # 测试提取
        batch_id = f"test_{int(datetime.now().timestamp())}"
        session_id = "test_session"
        
        result = await extractor.extract_multiple_cases(
            test_cases, batch_id, session_id
        )
        
        if result["status"] == "success":
            logger.info(f"案件提取测试成功: {result['processing_summary']}")
            return True
        else:
            logger.error(f"案件提取测试失败: {result.get('error')}")
            return False
        
    except Exception as e:
        logger.error(f"案件提取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始系统测试...")
    
    tests = [
        ("文件处理", test_file_processing),
        ("数据库连接", test_database_connection),
        ("模型连接", test_model_connection),
        ("案件提取", lambda: asyncio.run(test_case_extraction()))
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name}: ❌ 异常 - {e}")
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！系统准备就绪。")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置。")

if __name__ == "__main__":
    main()
