# 字段中文显示修改说明

## 🎯 修改目标

将前端提取的案件数据中的前几个字段显示为中文名称：
- `batch_id` → **批次号**
- `host_org` → **承办单位**
- `case_id` → **案件编号**
- `case_name` → **案件名称**

## ✅ 修改内容

### 1. 数据表格显示优化

**位置**: `streamlit_app.py` 第964-994行

**修改前**:
```python
column_config={
    col: st.column_config.TextColumn(
        col,  # 直接使用英文字段名
        width="medium",
        help=f"编辑{col}"
    ) for col in df.columns
}
```

**修改后**:
```python
# 定义字段名称映射
field_name_mapping = {
    'batch_id': '批次号',
    'host_org': '承办单位', 
    'case_id': '案件编号',
    'case_name': '案件名称'
}

# 创建列配置，为特定字段使用中文显示名称
column_config = {}
for col in df.columns:
    display_name = field_name_mapping.get(col, col)
    column_config[col] = st.column_config.TextColumn(
        display_name,  # 使用中文显示名称
        width="medium",
        help=f"编辑{display_name}"
    )
```

### 2. 关系图画廊案件信息卡片优化

**位置**: `streamlit_app.py` 第1177-1197行

**修改前**:
```python
# 案件信息卡片
st.markdown(f'''
<div class="case-card">
    <h4>{case_id}</h4>
    <p><strong>案件名称:</strong> {case_name}</p>
</div>
''', unsafe_allow_html=True)
```

**修改后**:
```python
# 获取案件详细信息
case_name = "未知案件"
batch_id = ""
host_org = ""
for result in individual_results:
    if result.get("case_id") == case_id:
        case_name = result.get("case_name", "未知案件")
        batch_id = result.get("batch_id", "")
        host_org = result.get("host_org", "")
        break

# 案件信息卡片 - 显示中文字段名
st.markdown(f'''
<div class="case-card">
    <h4>案件编号: {case_id}</h4>
    <p><strong>案件名称:</strong> {case_name}</p>
    {f'<p><strong>批次号:</strong> {batch_id}</p>' if batch_id else ''}
    {f'<p><strong>承办单位:</strong> {host_org}</p>' if host_org else ''}
</div>
''', unsafe_allow_html=True)
```

## 🎨 显示效果

### 数据表格
- **列标题**: 现在显示为中文名称
  - `batch_id` 列显示为 "批次号"
  - `host_org` 列显示为 "承办单位"
  - `case_id` 列显示为 "案件编号"
  - `case_name` 列显示为 "案件名称"

- **编辑提示**: 鼠标悬停时显示中文提示
  - "编辑批次号"
  - "编辑承办单位"
  - "编辑案件编号"
  - "编辑案件名称"

### 关系图画廊
- **案件信息卡片**: 显示更完整的中文字段信息
  - 标题: "案件编号: CASE001"
  - 案件名称: "网络诈骗案"
  - 批次号: "batch_20241216"
  - 承办单位: "某某公安局"

## 🔧 技术实现

### 字段映射机制
```python
field_name_mapping = {
    'batch_id': '批次号',
    'host_org': '承办单位', 
    'case_id': '案件编号',
    'case_name': '案件名称'
}
```

### 动态显示名称
```python
display_name = field_name_mapping.get(col, col)
# 如果字段在映射中，使用中文名称；否则使用原字段名
```

### 条件显示
```python
{f'<p><strong>批次号:</strong> {batch_id}</p>' if batch_id else ''}
# 只有当字段有值时才显示该行
```

## 📱 用户体验改进

### 1. 更直观的字段理解
- **修改前**: 用户看到 `batch_id`、`host_org` 等英文字段名
- **修改后**: 用户看到 "批次号"、"承办单位" 等中文字段名

### 2. 更完整的信息展示
- **修改前**: 关系图卡片只显示案件编号和名称
- **修改后**: 关系图卡片显示案件编号、名称、批次号、承办单位

### 3. 一致的中文界面
- 所有用户界面元素都使用中文显示
- 提供更好的本地化体验

## 🧪 测试验证

### 测试步骤
1. 运行主应用：`streamlit run streamlit_app.py --server.port 8510`
2. 上传Excel文件进行批量处理
3. 查看"📋 提取的案件数据"部分的表格列标题
4. 查看"🖼️ 案件人物关系图画廊"中的案件信息卡片

### 预期结果
- ✅ 数据表格的列标题显示为中文
- ✅ 关系图卡片显示完整的中文字段信息
- ✅ 鼠标悬停提示使用中文
- ✅ 所有字段名称本地化

## 📋 影响范围

### 修改的文件
- `streamlit_app.py` - 主应用文件

### 修改的功能模块
1. **数据表格显示** - 可编辑数据表格的列配置
2. **关系图画廊** - 案件信息卡片显示

### 保持不变的部分
- 数据处理逻辑
- 文件导入导出功能
- 数据库操作
- 报告生成功能

## 🎯 总结

通过这次修改，前端界面现在提供了更好的中文本地化体验：

1. **字段名称中文化** - 核心字段使用中文显示
2. **信息展示完整化** - 关系图卡片显示更多有用信息
3. **用户体验优化** - 更直观、更易理解的界面

所有修改都保持了原有功能的完整性，只是改进了显示效果和用户体验。
