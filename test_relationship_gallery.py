#!/usr/bin/env python3
"""
测试关系图画廊滚动容器功能
"""

import streamlit as st
import base64
import io
from PIL import Image, ImageDraw, ImageFont
import random

def create_test_image(case_id, case_name):
    """创建测试用的关系图图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制简单的关系图
    draw.rectangle([50, 50, 350, 250], outline='blue', width=2)
    draw.text((60, 60), f"案件: {case_id}", fill='black')
    draw.text((60, 90), f"名称: {case_name}", fill='black')
    draw.text((60, 120), "人物关系图", fill='blue')
    
    # 绘制一些节点和连线
    nodes = [(100, 150), (200, 150), (300, 150), (150, 200), (250, 200)]
    for i, (x, y) in enumerate(nodes):
        draw.ellipse([x-15, y-15, x+15, y+15], fill='lightblue', outline='blue')
        draw.text((x-5, y-5), str(i+1), fill='black')
    
    # 绘制连线
    connections = [(0, 1), (1, 2), (0, 3), (1, 4), (2, 4)]
    for start, end in connections:
        x1, y1 = nodes[start]
        x2, y2 = nodes[end]
        draw.line([x1, y1, x2, y2], fill='gray', width=2)
    
    # 转换为base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    return base64.b64encode(buffer.getvalue()).decode()

def create_test_data():
    """创建测试数据"""
    test_cases = [
        ("CASE001", "网络诈骗案"),
        ("CASE002", "洗钱案件"),
        ("CASE003", "组织犯罪案"),
        ("CASE004", "经济诈骗案"),
        ("CASE005", "网络赌博案"),
        ("CASE006", "传销组织案"),
        ("CASE007", "非法集资案"),
        ("CASE008", "贪污受贿案"),
    ]
    
    relationship_images = {}
    individual_results = []
    
    for case_id, case_name in test_cases:
        # 创建测试图片
        image_base64 = create_test_image(case_id, case_name)
        relationship_images[case_id] = image_base64
        
        # 创建对应的案件结果数据
        individual_results.append({
            "case_id": case_id,
            "case_name": case_name,
            "status": "success"
        })
    
    return {
        "relationship_images": relationship_images,
        "extraction": {
            "individual_results": individual_results
        }
    }

def main():
    st.set_page_config(
        page_title="关系图画廊测试",
        page_icon="🖼️",
        layout="wide"
    )
    
    st.title("🖼️ 关系图画廊滚动容器测试")
    
    # 创建测试按钮
    if st.button("生成测试数据"):
        st.session_state.current_batch_data = create_test_data()
        st.success("测试数据已生成！")
    
    # 清除测试数据按钮
    if st.button("清除测试数据"):
        if 'current_batch_data' in st.session_state:
            del st.session_state.current_batch_data
        st.success("测试数据已清除！")
    
    # 显示说明
    st.markdown("""
    ## 测试说明
    
    1. 点击"生成测试数据"按钮创建8个测试案件的关系图
    2. 测试滚动容器功能：
       - 查看滚动容器的外观和边框
       - 测试滚动功能（当内容超过600px高度时）
       - 测试搜索功能
       - 测试批量下载功能
    3. 点击"清除测试数据"清空数据
    
    ## 预期效果
    
    - 关系图应该显示在一个带蓝色边框的容器内
    - 容器顶部有渐变色标题栏显示案件数量
    - 内容区域可以滚动，最大高度600px
    - 搜索框可以过滤案件
    - 多个案件时显示批量下载按钮
    """)
    
    # 导入并调用关系图显示函数
    try:
        # 这里需要导入原始的display_relationship_gallery函数
        # 由于模块导入的复杂性，我们直接复制函数逻辑
        display_relationship_gallery_test()
    except Exception as e:
        st.error(f"显示关系图时出错: {e}")

def display_relationship_gallery_test():
    """测试版本的关系图画廊显示函数"""
    if not hasattr(st.session_state, 'current_batch_data') or not st.session_state.current_batch_data:
        st.info("请先生成测试数据")
        return

    relationship_images = st.session_state.current_batch_data.get("relationship_images", {})

    if relationship_images:
        # 显示统计信息
        total_images = len(relationship_images)
        st.markdown(f'''
        <div style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 1px solid #bee5eb; border-radius: 8px; padding: 0.8rem; margin: 0.5rem 0; text-align: center; font-size: 0.9em; color: #0c5460;">
            📊 <strong>关系图统计:</strong> 共有 <strong>{total_images}</strong> 个案件的人物关系图
        </div>
        ''', unsafe_allow_html=True)

        # 搜索功能
        search_term = st.text_input("🔍 搜索案件编号或名称", placeholder="输入案件编号或名称进行搜索")

        # 过滤图片
        filtered_images = relationship_images
        if search_term:
            filtered_images = {k: v for k, v in relationship_images.items() if search_term.lower() in k.lower()}

        if filtered_images:
            # 显示过滤后的统计和批量下载
            if search_term:
                st.markdown(f'''
                <div style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 1px solid #bee5eb; border-radius: 8px; padding: 0.8rem; margin: 0.5rem 0; text-align: center; font-size: 0.9em; color: #0c5460;">
                    🔍 <strong>搜索结果:</strong> 找到 <strong>{len(filtered_images)}</strong> 个匹配的案件
                </div>
                ''', unsafe_allow_html=True)

            # 批量下载功能
            if len(filtered_images) > 1:
                col1, col2 = st.columns([3, 1])
                with col2:
                    # 创建ZIP文件
                    import zipfile

                    zip_buffer = io.BytesIO()
                    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                        for case_id, image_base64 in filtered_images.items():
                            image_data = base64.b64decode(image_base64)
                            zip_file.writestr(f"{case_id}_关系图.png", image_data)

                    zip_buffer.seek(0)
                    st.download_button(
                        label="📦 批量下载所有关系图",
                        data=zip_buffer.getvalue(),
                        file_name=f"关系图批量下载_{len(filtered_images)}个案件.zip",
                        mime="application/zip",
                        use_container_width=True,
                        key="batch_download_all_images_test"
                    )

            # 开始滚动容器
            st.markdown(f'''
            <div style="border: 3px solid #3498db; border-radius: 15px; padding: 1.5rem; margin: 2rem 0; background: linear-gradient(145deg, #f8f9fa, #e9ecef); box-shadow: 0 8px 16px rgba(52, 152, 219, 0.2), inset 0 1px 3px rgba(255, 255, 255, 0.8);">
                <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 1rem 1.5rem; margin: -1.5rem -1.5rem 1.5rem -1.5rem; border-radius: 12px 12px 0 0; font-weight: bold; font-size: 1.2em; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.15);">
                    🖼️ 案件人物关系图画廊 ({len(filtered_images)} 个案件)
                </div>
                <div style="max-height: 650px; overflow-y: auto; overflow-x: hidden; padding: 1rem; border-radius: 10px; background-color: white; border: 2px solid #e9ecef; box-shadow: inset 0 2px 8px rgba(0,0,0,0.05); position: relative;">
                    <div style="position: absolute; top: 10px; right: 20px; background: rgba(52, 152, 219, 0.9); color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8em; z-index: 10;">📜 可滚动查看更多</div>
            ''', unsafe_allow_html=True)

            # 使用列布局显示图片
            cols = st.columns(2)  # 每行显示2个图片

            for i, (case_id, image_base64) in enumerate(filtered_images.items()):
                with cols[i % 2]:
                    # 获取案件名称
                    extraction = st.session_state.current_batch_data.get("extraction", {})
                    individual_results = extraction.get("individual_results", [])
                    case_name = "未知案件"
                    for result in individual_results:
                        if result.get("case_id") == case_id:
                            case_name = result.get("case_name", "未知案件")
                            break

                    st.markdown(f'''
                    <div style="border: 1px solid #dee2e6; border-radius: 8px; padding: 1rem; background-color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s ease, box-shadow 0.2s ease;">
                        <h4 style="margin-top: 0; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 0.5rem;">{case_id}</h4>
                        <p><strong>案件名称:</strong> {case_name}</p>
                    </div>
                    ''', unsafe_allow_html=True)

                    # 显示图片
                    image_data = base64.b64decode(image_base64)
                    st.image(image_data, caption=f"{case_id} 人物关系图", use_container_width=True)

                    # 下载按钮
                    st.download_button(
                        label="📥 下载关系图",
                        data=image_data,
                        file_name=f"{case_id}.png",
                        mime="image/png",
                        use_container_width=True,
                        key=f"download_img_{case_id}"
                    )

            # 结束滚动容器
            st.markdown('''
                </div>
            </div>
            ''', unsafe_allow_html=True)
        else:
            # 搜索无结果的情况
            st.markdown(f'''
            <div style="text-align: center; padding: 2rem; color: #6c757d; background-color: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6; margin: 1rem 0;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
                <h3>没有找到匹配的关系图</h3>
                <p>搜索词 "<strong>{search_term}</strong>" 没有匹配到任何案件</p>
                <p>请尝试其他关键词或清空搜索框查看所有关系图</p>
            </div>
            ''', unsafe_allow_html=True)
    else:
        # 完全没有关系图的情况
        st.markdown('''
        <div style="text-align: center; padding: 2rem; color: #6c757d; background-color: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6; margin: 1rem 0;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
            <h3>暂无人物关系图</h3>
            <p>请先完成案件处理，系统将自动生成人物关系图</p>
        </div>
        ''', unsafe_allow_html=True)

if __name__ == "__main__":
    main()
