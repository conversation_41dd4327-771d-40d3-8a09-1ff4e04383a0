# 用户需求功能实现总结

## 📋 需求描述

将 `BatchCaseExtractionAgent` 中的固定分析需求改为从前端"分析需求"输入框获取，并提供默认的完整分析需求模板。

## ✅ 实现内容

### 1. 前端修改 (`streamlit_app.py`)

#### 添加分析需求输入框
```python
# 分析需求输入
default_analysis_requirements = """任务目标：
1. 组织架构解析（主犯/从犯认定、分工逻辑）
2. 结构化数据提取（CSV格式，14列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的组织和所有提及到的人物信息：实体类型（人员或组织等），姓名/代号，性别，年龄，身份证号，户籍地/现居地，文化程度，直接上级，所属组织，组织层级，分工角色,关联工具/行为,司法处置结果,经济收益（元）

"年龄"要素提取要求：
其中"年龄" 要素，优先从案件内容中分析获取，若无法直接获得"年龄"要素，则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄" 

输出CSV格式（14列）：
实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织之间的多层级关系，生成Mermaid格式的关系图代码。"""

user_requirements = st.text_area(
    "分析需求",
    value=default_analysis_requirements,
    height=300,
    help="请输入具体的分析需求和任务目标"
)
```

#### 特点
- **默认内容**: 包含完整的分析需求模板
- **高度**: 300px，便于查看和编辑
- **帮助提示**: 指导用户如何使用
- **可编辑**: 用户可以根据需要修改分析需求

### 2. 后端修改 (`multi_agents.py`)

#### BatchCaseExtractionAgent 类修改

##### 初始化方法
```python
def __init__(self, model_manager: ModelManager, session_manager: SessionManager, max_concurrent: int = 10):
    self.model_client = model_manager.get_client()
    self.session_manager = session_manager
    self.max_concurrent = max_concurrent
    self.agent = None  # 延迟初始化，等待用户需求
    
def _initialize_agent(self, user_requirements: str = None):
    """初始化或重新初始化智能体"""
    self.agent = AssistantAgent(
        name="batch_case_extractor",
        model_client=self.model_client,
        system_message=self._get_system_message(user_requirements)
    )
```

##### 系统消息生成方法
```python
def _get_system_message(self, user_requirements: str = None) -> str:
    # 默认分析需求
    default_requirements = """..."""  # 与前端相同的默认内容
    
    # 使用用户提供的需求，如果没有则使用默认需求
    analysis_requirements = user_requirements.strip() if user_requirements else default_requirements
    
    return f"""你是一位走私案件资深分析专家，需要完成案件分析和要素提取。

{analysis_requirements}

返回格式：
{{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}}
"""
```

##### 案件提取方法
```python
async def extract_multiple_cases(self, cases_data: List[Dict[str, Any]], batch_id: str, session_id: str,
                               user_requirements: str = None, progress_callback=None) -> Dict[str, Any]:
    """并发提取多个案件的信息"""
    try:
        # 初始化智能体（如果还没有初始化或需求发生变化）
        if self.agent is None:
            self._initialize_agent(user_requirements)
        
        # ... 其余处理逻辑
```

#### 工作流处理方法
```python
# 在 process_multi_case_file 中传递用户需求
extraction_result = await self.batch_extractor.extract_multiple_cases(
    cases_data, batch_id, session_id, user_requirements, progress_callback
)
```

## 🔄 数据流程

```
前端输入框 (user_requirements)
    ↓
process_multi_case_file(user_requirements)
    ↓
extract_multiple_cases(user_requirements)
    ↓
_initialize_agent(user_requirements)
    ↓
_get_system_message(user_requirements)
    ↓
生成包含用户需求的 system_message
    ↓
AI 智能体使用自定义需求进行分析
```

## 🎯 功能特点

### 1. 灵活性
- **自定义分析**: 用户可以根据具体需求修改分析要求
- **动态生效**: 修改后立即生效，无需重启
- **完全可控**: 支持修改分析步骤、输出格式、提取字段等

### 2. 易用性
- **默认模板**: 提供完整的分析需求模板
- **即开即用**: 不修改默认内容也能正常工作
- **直观编辑**: 大文本框便于查看和编辑

### 3. 兼容性
- **向后兼容**: 现有代码无需修改
- **可选增强**: 新功能是可选的，不影响现有功能
- **默认值**: 所有新参数都有合理的默认值

## 📊 使用场景

### 1. 标准分析
用户使用默认需求进行标准的案件分析，包含：
- 组织架构解析
- 14列CSV数据提取
- Mermaid关系图生成

### 2. 自定义分析
用户根据特定需求修改分析要求：
- 只提取人员信息
- 添加新的分析维度
- 修改输出格式
- 调整分析步骤

### 3. 特殊场景
- 针对特定案件类型的专门分析
- 符合特定法律要求的数据提取
- 适应不同业务部门的需求

## 🚀 使用方法

### 1. 默认使用
1. 打开系统，查看"分析需求"框中的默认内容
2. 直接上传文件开始分析
3. 系统使用默认的完整分析需求

### 2. 自定义使用
1. 在"分析需求"框中修改分析要求
2. 可以添加、删除或修改分析步骤
3. 可以调整输出格式和字段要求
4. 上传文件，系统使用自定义需求进行分析

### 3. 模板参考
默认模板包含：
- **任务目标**: 明确分析目标
- **执行步骤**: 详细的分析流程
- **输出格式**: 具体的数据格式要求
- **特殊要求**: 如年龄计算逻辑等

## ✨ 优势

1. **提高灵活性**: 用户可以根据实际需求调整分析要求
2. **提升效率**: 避免固定模式，适应多样化需求
3. **降低门槛**: 提供完整模板，用户可以在此基础上修改
4. **保持兼容**: 不影响现有功能，平滑升级
5. **增强体验**: 用户有更多控制权，满足个性化需求

## 🔧 技术实现

- **前端**: Streamlit text_area 组件
- **后端**: 动态 system_message 生成
- **参数传递**: 完整的调用链支持
- **智能体**: 延迟初始化，支持动态配置
- **兼容性**: 默认参数确保向后兼容

这个实现完美地满足了用户需求，既保持了系统的灵活性，又确保了易用性和兼容性。
