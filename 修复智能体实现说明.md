# 修复智能体实现说明

## 🎯 实现目标

1. **去掉matplotlib备用方案**：移除所有matplotlib相关的备用图片生成逻辑
2. **新增修复智能体**：实现智能修复失败的案件处理和关系图生成
3. **可配置修复次数**：支持设置每个案件的最大修复次数（默认2次）
4. **智能错误处理**：当超过修复次数后直接跳过案件并显示错误

## ✅ 已完成的修改

### 一. 移除matplotlib备用方案

#### 1. 删除matplotlib备用方案调用
**文件**: `agents.py`
**位置**: RelationshipVisualizationAgent.render_mermaid_to_image方法

**修改前**:
```python
else:
    # Docker失败，使用matplotlib备用方案
    logging.warning("Docker生成失败，使用matplotlib备用方案")
    return self._render_with_matplotlib_fallback(mermaid_code, session_id, case_name)
```

**修改后**:
```python
else:
    # Docker失败，返回错误信息
    logging.error("Docker生成失败，无法生成关系图")
    return {
        "status": "error",
        "error": "Docker生成失败，无法生成关系图",
        "mermaid_code": mermaid_code
    }
```

#### 2. 删除matplotlib相关方法
- 删除 `_render_with_matplotlib_fallback` 方法
- 删除 `_parse_mermaid` 方法  
- 删除 `_calculate_positions` 方法
- 删除 `setup_matplotlib_font` 函数

### 二. 新增修复智能体

#### 1. RepairAgent类实现
**文件**: `agents.py`
**位置**: 新增类

```python
class RepairAgent:
    """修复智能体 - 用于修复失败的案件处理和关系图生成"""
    
    def __init__(self, model_manager: ModelManager):
        self.model_client = model_manager.get_client()
        self.agent = AssistantAgent(
            name="repair_agent",
            model_client=self.model_client,
            system_message=self._get_system_message()
        )
```

#### 2. 修复智能体功能

**案件要素提取修复**:
```python
async def repair_case_extraction(self, error_type: str, error_message: str, 
                                case_data: Dict[str, Any], original_requirements: str) -> Dict[str, Any]:
```

**Mermaid关系图修复**:
```python
async def repair_mermaid_generation(self, error_message: str, mermaid_code: str, 
                                  original_requirements: str, case_info: dict) -> Dict[str, Any]:
```

#### 3. 修复智能体系统提示词

```python
def _get_system_message(self) -> str:
    return """你是一位专业的案件分析修复专家，专门负责修复失败的案件要素提取和关系图生成任务。

你的任务是：
1. 分析失败的原因
2. 根据原始需求和错误信息，重新进行分析和提取
3. 确保输出格式正确且完整

修复原则：
- 仔细分析错误原因，避免重复相同错误
- 严格按照原始需求进行分析
- 确保CSV数据格式正确，字段完整
- 确保Mermaid代码语法正确，关系清晰
- 如果原始数据不足，合理推断或标注"未知"

返回格式：
{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据", 
    "mermaid_code": "Mermaid关系图代码"
}
"""
```

### 三. 集成修复智能体到现有系统

#### 1. BatchCaseExtractionAgent修改

**构造函数增加修复智能体**:
```python
def __init__(self, model_manager: ModelManager, session_manager: SessionManager, 
             max_concurrent: int = 10, max_repair_attempts: int = 2):
    # ... 原有代码 ...
    self.max_repair_attempts = max_repair_attempts  # 每个案件最大修复次数
    self.repair_agent = RepairAgent(model_manager)  # 修复智能体
```

**新增_extract_single_case方法**:
```python
async def _extract_single_case(self, case_data: Dict[str, Any], batch_id: str, 
                             session_id: str, repair_count: int = 0) -> Dict[str, Any]:
```

**修复逻辑实现**:
```python
except Exception as e:
    # 如果还有修复机会，尝试使用修复智能体
    if repair_count < self.max_repair_attempts:
        logging.info(f"尝试修复案件 {case_id}，第 {repair_count + 1} 次")
        try:
            repair_result = await self.repair_agent.repair_case_extraction(
                error_type="extraction_failure",
                error_message=str(e),
                case_data=case_data,
                original_requirements=self._get_system_message()
            )
            # 处理修复结果...
        except Exception as repair_error:
            # 修复失败，递归重试
            return await self._extract_single_case(case_data, batch_id, session_id, repair_count + 1)
    
    # 超过最大修复次数，返回错误
    return {
        "status": "error",
        "case_id": case_data.get('案件编号', ''),
        "error": str(e),
        "repair_count": repair_count,
        "max_repairs_exceeded": True
    }
```

#### 2. RelationshipVisualizationAgent修改

**构造函数增加修复智能体**:
```python
def __init__(self, session_manager, model_manager=None, max_repair_attempts: int = 2):
    self.session_manager = session_manager
    self.model_manager = model_manager
    self.max_repair_attempts = max_repair_attempts
    self.repair_agent = RepairAgent(model_manager) if model_manager else None
```

**render_mermaid_to_image方法修改**:
```python
async def render_mermaid_to_image(self, mermaid_code: str, session_id: str, 
                                case_name: str = "relationship", csv_data: str = None, 
                                user_requirements: str = None, repair_count: int = 0) -> Dict[str, Any]:
```

**Docker失败后的修复逻辑**:
```python
else:
    # Docker失败，尝试使用修复智能体
    if repair_count < self.max_repair_attempts and self.repair_agent:
        logging.info(f"尝试修复Mermaid代码，第 {repair_count + 1} 次")
        try:
            repair_result = await self.repair_agent.repair_mermaid_generation(
                error_message=error_message,
                mermaid_code=mermaid_code,
                original_requirements=user_requirements or "",
                case_info=case_info
            )
            
            if repair_result["status"] == "success":
                # 使用修复后的Mermaid代码重新尝试
                repaired_mermaid = repair_result.get("mermaid_code", "")
                if repaired_mermaid:
                    return await self.render_mermaid_to_image(
                        repaired_mermaid, session_id, case_name, csv_data, 
                        user_requirements, repair_count + 1
                    )
        except Exception as repair_error:
            # 修复失败，递归重试
            return await self.render_mermaid_to_image(
                mermaid_code, session_id, case_name, csv_data, 
                user_requirements, repair_count + 1
            )
    
    # 超过最大修复次数，返回错误
    return {
        "status": "error",
        "error": error_message,
        "mermaid_code": mermaid_code,
        "repair_count": repair_count,
        "max_repairs_exceeded": True
    }
```

### 四. 前端配置界面

#### 1. streamlit_app.py修改

**新增修复次数配置**:
```python
# 初始化session state
if 'max_repair_attempts' not in st.session_state:
    st.session_state.max_repair_attempts = 2
```

**侧边栏添加修复次数滑块**:
```python
new_max_repair_attempts = st.slider(
    "每个案件最大修复次数",
    min_value=0,
    max_value=5,
    value=st.session_state.max_repair_attempts,
    help="当案件处理失败时，修复智能体的最大重试次数"
)
if new_max_repair_attempts != st.session_state.max_repair_attempts:
    st.session_state.max_repair_attempts = new_max_repair_attempts
    # 重新初始化orchestrator
    st.session_state.orchestrator = MultiCaseAnalysisOrchestrator(
        max_concurrent=st.session_state.max_concurrent,
        max_repair_attempts=new_max_repair_attempts
    )
    st.rerun()
```

#### 2. multi_agents.py修改

**MultiCaseAnalysisOrchestrator构造函数**:
```python
def __init__(self, max_concurrent: int = 10, max_repair_attempts: int = 2):
    self.session_manager = SessionManager()
    self.model_manager = ModelManager()
    self.db_manager = DatabaseManager()
    self.file_processor = ExcelFileProcessor()
    self.batch_extractor = BatchCaseExtractionAgent(
        self.model_manager, self.session_manager, max_concurrent, max_repair_attempts
    )
    self.batch_db_inserter = BatchDatabaseInsertAgent(self.db_manager, self.session_manager)
    self.relationship_visualizer = RelationshipVisualizationAgent(
        self.session_manager, self.model_manager, max_repair_attempts
    )
```

## 🔧 技术特点

### 1. 智能修复机制
- **错误分析**: 修复智能体会分析失败原因，避免重复相同错误
- **上下文保持**: 传递原始需求和错误信息，确保修复的准确性
- **格式保证**: 确保修复后的输出格式正确且完整

### 2. 可配置修复次数
- **默认设置**: 每个案件默认最大修复2次
- **用户可调**: 通过前端滑块可调整0-5次
- **实时生效**: 参数变化时自动重新初始化相关组件

### 3. 递归修复逻辑
- **逐步修复**: 每次修复失败后递增修复计数
- **智能终止**: 超过最大次数后停止修复，返回错误信息
- **状态跟踪**: 记录修复次数和是否超过限制

### 4. 异步处理支持
- **async/await**: 所有修复方法都支持异步处理
- **并发安全**: 修复过程不影响其他案件的并发处理
- **性能优化**: 避免阻塞主线程

## 📊 修复流程

### 案件要素提取修复流程
```
案件处理失败 → 检查修复次数 → 调用修复智能体 → 重新提取 → 成功/继续修复 → 超限返回错误
```

### 关系图生成修复流程  
```
Docker生成失败 → 检查修复次数 → 调用修复智能体 → 修复Mermaid代码 → 重新生成 → 成功/继续修复 → 超限返回错误
```

## 🎯 使用效果

### 1. 提高成功率
- **智能修复**: 自动修复常见的格式错误和语法问题
- **减少失败**: 大幅降低案件处理失败的概率
- **提升质量**: 修复后的输出更加规范和准确

### 2. 用户体验优化
- **可视化配置**: 通过滑块直观调整修复次数
- **实时反馈**: 显示修复进度和结果
- **错误提示**: 清晰显示超过修复次数的案件

### 3. 系统稳定性
- **容错能力**: 增强系统对异常情况的处理能力
- **资源控制**: 通过修复次数限制避免无限重试
- **性能保证**: 异步处理确保系统响应性

## 🚀 总结

通过实现修复智能体，系统现在具备了：

1. **智能错误恢复**: 自动分析和修复失败的案件处理
2. **可配置容错**: 用户可根据需要调整修复策略
3. **高可用性**: 大幅提升批量处理的成功率
4. **用户友好**: 提供直观的配置界面和错误反馈

修复智能体的引入使得系统更加智能化和稳定，能够有效处理各种异常情况，为用户提供更可靠的案件分析服务！🎉
