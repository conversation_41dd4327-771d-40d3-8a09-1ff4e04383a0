# 修复智能体功能实现说明

## 🎯 实现目标

1. **新增修复智能体**：专门处理失败的案件要素提取和关系图生成
2. **案件要素提取修复**：当处理失败时，使用修复智能体重新分析
3. **关系图生成修复**：去掉matplotlib备用方案，使用修复智能体修复Mermaid代码
4. **可配置修复次数**：每个案件修复次数上限可设置，默认2次

## ✅ 实现内容

### 一. 修复智能体类 (RepairAgent)

#### 1. 基本结构
```python
class RepairAgent:
    """修复智能体 - 用于修复失败的案件处理和关系图生成"""
    
    def __init__(self, model_manager: ModelManager):
        self.model_client = model_manager.get_client()
        self.agent = AssistantAgent(
            name="repair_agent",
            model_client=self.model_client,
            system_message=self._get_system_message()
        )
```

#### 2. 系统消息
```python
def _get_system_message(self) -> str:
    return """你是一位专业的案件分析修复专家，专门负责修复失败的案件要素提取和关系图生成任务。

你的任务是：
1. 分析失败的原因
2. 根据原始需求和错误信息，重新进行分析和提取
3. 确保输出格式正确且完整

修复原则：
- 仔细分析错误原因，避免重复相同错误
- 严格按照原始需求进行分析
- 确保CSV数据格式正确，字段完整
- 确保Mermaid代码语法正确，关系清晰
- 如果原始数据不足，合理推断或标注"未知"

返回格式：
{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}
"""
```

#### 3. 案件要素提取修复
```python
async def repair_extraction(self, error_type: str, error_message: str, original_content: str, 
                          original_requirements: str, case_info: dict) -> dict:
    """修复案件要素提取失败"""
    try:
        prompt = f"""
修复任务：案件要素提取失败

错误类别：{error_type}
错误信息：{error_message}

原始案件信息：
案件编号：{case_info.get('case_id', '')}
案件名称：{case_info.get('case_name', '')}
承办单位：{case_info.get('host_org', '')}

原始需求：
{original_requirements}

原始案件内容：
{original_content}

请分析失败原因，重新进行案件要素提取，确保输出格式正确。
"""
        # ... 处理逻辑
```

#### 4. Mermaid关系图修复
```python
async def repair_mermaid(self, error_message: str, mermaid_code: str, 
                       original_requirements: str, case_info: dict) -> dict:
    """修复Mermaid关系图生成失败"""
    try:
        prompt = f"""
修复任务：Mermaid关系图生成失败

错误信息：{error_message}

原始Mermaid代码：
{mermaid_code}

案件信息：
案件编号：{case_info.get('case_id', '')}
案件名称：{case_info.get('case_name', '')}

原始需求：
{original_requirements}

请分析Mermaid代码的语法错误，修复并重新生成正确的Mermaid关系图代码。
确保：
1. 语法正确，符合Mermaid规范
2. 节点命名规范，避免特殊字符
3. 关系线条清晰，标签准确
4. 整体结构合理，易于理解
"""
        # ... 处理逻辑
```

### 二. 批量案件提取智能体修改

#### 1. 添加修复智能体支持
```python
class BatchCaseExtractionAgent:
    def __init__(self, model_manager: ModelManager, session_manager: SessionManager, 
                 max_concurrent: int = 10, max_repair_attempts: int = 2):
        self.model_client = model_manager.get_client()
        self.session_manager = session_manager
        self.max_concurrent = max_concurrent
        self.max_repair_attempts = max_repair_attempts  # 每个案件最大修复次数
        self.agent = None
        self.repair_agent = RepairAgent(model_manager)  # 修复智能体
```

#### 2. 案件提取方法修改
```python
async def _extract_single_case(self, case_data: Dict[str, Any], batch_id: str, session_id: str, 
                             user_requirements: str = None, repair_count: int = 0) -> Dict[str, Any]:
    """提取单个案件信息，支持修复重试"""
    try:
        # ... 原有处理逻辑
        
    except Exception as e:
        logging.error(f"单个案件信息提取失败: {e}")
        
        # 如果还有修复机会，尝试使用修复智能体
        if repair_count < self.max_repair_attempts:
            logging.info(f"尝试修复案件 {case_id}，第 {repair_count + 1} 次")
            try:
                case_info = {
                    'case_id': case_id,
                    'case_name': case_name,
                    'host_org': host_org
                }
                
                repair_result = await self.repair_agent.repair_extraction(
                    error_type="extraction_failure",
                    error_message=str(e),
                    original_content=case_content,
                    original_requirements=user_requirements or "",
                    case_info=case_info
                )
                
                if repair_result["status"] == "success":
                    # 处理修复后的数据
                    # ... 返回修复后的结果
                else:
                    # 修复失败，递归重试
                    return await self._extract_single_case(case_data, batch_id, session_id, user_requirements, repair_count + 1)
                    
            except Exception as repair_error:
                # 修复失败，递归重试
                return await self._extract_single_case(case_data, batch_id, session_id, user_requirements, repair_count + 1)
        
        # 超过最大修复次数，返回错误
        return {
            "status": "error",
            "case_id": case_data.get('案件编号', ''),
            "error": str(e),
            "repair_count": repair_count,
            "max_repairs_exceeded": True
        }
```

### 三. 关系图可视化智能体修改

#### 1. 去掉matplotlib备用方案
```python
class RelationshipVisualizationAgent:
    """人物关系图可视化智能体 - 支持修复功能，去掉matplotlib备用方案"""
    
    def __init__(self, session_manager, model_manager=None, max_repair_attempts: int = 2):
        self.session_manager = session_manager
        self.model_manager = model_manager
        self.max_repair_attempts = max_repair_attempts
        self.repair_agent = RepairAgent(model_manager) if model_manager else None
```

#### 2. 修复逻辑
```python
async def render_mermaid_to_image(self, mermaid_code: str, session_id: str, case_name: str = "relationship", 
                                user_requirements: str = None, repair_count: int = 0) -> Dict[str, Any]:
    """将Mermaid代码渲染为图片，支持修复重试，去掉matplotlib备用方案"""
    try:
        # ... Docker生成逻辑
        
        if docker_success and output_path.exists():
            # 成功生成
            return {
                "status": "success",
                # ... 其他返回数据
                "repair_count": repair_count
            }
        else:
            # Docker失败，不使用matplotlib备用方案，而是尝试修复
            error_message = "Docker生成失败，无法生成关系图"
            
            # 如果还有修复机会且有修复智能体，尝试修复
            if repair_count < self.max_repair_attempts and self.repair_agent:
                logging.info(f"尝试修复Mermaid代码，第 {repair_count + 1} 次")
                try:
                    case_info = {
                        'case_id': case_name,
                        'case_name': case_name
                    }
                    
                    repair_result = await self.repair_agent.repair_mermaid(
                        error_message=error_message,
                        mermaid_code=mermaid_code,
                        original_requirements=user_requirements or "",
                        case_info=case_info
                    )
                    
                    if repair_result["status"] == "success":
                        # 使用修复后的Mermaid代码重新尝试
                        repaired_mermaid = repair_result.get("mermaid_code", "")
                        if repaired_mermaid:
                            return await self.render_mermaid_to_image(
                                repaired_mermaid, session_id, case_name, user_requirements, repair_count + 1
                            )
                    
                    # 修复失败，递归重试
                    return await self.render_mermaid_to_image(
                        mermaid_code, session_id, case_name, user_requirements, repair_count + 1
                    )
                    
                except Exception as repair_error:
                    # 修复失败，递归重试
                    return await self.render_mermaid_to_image(
                        mermaid_code, session_id, case_name, user_requirements, repair_count + 1
                    )
            
            # 超过最大修复次数或没有修复智能体，返回错误
            return {
                "status": "error",
                "error": error_message,
                "repair_count": repair_count,
                "max_repairs_exceeded": True
            }
```

### 四. 前端配置界面

#### 1. 修复次数配置
```python
# 修复智能体配置
st.markdown("### 🔧 修复智能体配置")
max_repair_attempts = st.number_input(
    "每个案件最大修复次数",
    min_value=0,
    max_value=5,
    value=2,
    help="当案件处理失败时，修复智能体的最大重试次数"
)
```

#### 2. 参数传递
```python
# 在文件上传处理中
if st.button("🚀 开始处理", type="primary", disabled=st.session_state.processing):
    return uploaded_file, analysis_requirements, max_repair_attempts

# 在主函数中
uploaded_file, user_requirements, max_repair_attempts = display_file_upload_section()

# 在处理调用中
result = asyncio.run(st.session_state.orchestrator.process_multi_case_file(
    saved_file_path,
    st.session_state.current_session_id,
    user_requirements,
    st.session_state.max_concurrent,
    max_repair_attempts,  # 传递修复次数
    progress_callback
))
```

## 🔧 技术实现

### 1. 修复逻辑
- **递归重试**：修复失败时递归调用，直到达到最大次数
- **状态跟踪**：记录修复次数和修复状态
- **错误分析**：修复智能体分析具体错误原因

### 2. 错误处理
- **分类处理**：区分案件提取失败和关系图生成失败
- **上下文保持**：保持原始需求和案件信息
- **优雅降级**：超过修复次数时返回详细错误信息

### 3. 性能优化
- **并发控制**：修复过程不影响其他案件的并发处理
- **资源管理**：合理控制修复次数，避免无限重试
- **日志记录**：详细记录修复过程和结果

## 🎯 功能特点

### 1. 智能修复
- **专业分析**：修复智能体专门训练用于错误分析和修复
- **上下文理解**：充分理解原始需求和错误信息
- **格式保证**：确保修复后的输出格式正确

### 2. 灵活配置
- **可调次数**：用户可以设置每个案件的最大修复次数
- **默认合理**：默认2次修复，平衡效果和性能
- **范围控制**：限制在0-5次之间，避免过度重试

### 3. 完整集成
- **无缝集成**：修复功能完全集成到现有工作流中
- **状态透明**：用户可以看到修复过程和结果
- **向后兼容**：不影响现有功能的正常使用

## 🧪 测试验证

### 测试场景
1. **正常处理**：验证修复功能不影响正常案件处理
2. **提取失败**：模拟案件要素提取失败，验证修复功能
3. **关系图失败**：模拟Mermaid生成失败，验证修复功能
4. **修复次数**：验证修复次数限制功能
5. **配置变更**：验证修复次数配置的实时生效

### 预期结果
- ✅ 修复智能体能够成功修复大部分失败案例
- ✅ 修复次数限制有效防止无限重试
- ✅ 用户界面友好，配置简单
- ✅ 性能影响最小，不影响正常处理速度
- ✅ 错误信息详细，便于问题诊断

## 🎉 总结

修复智能体功能的实现大大提升了系统的鲁棒性和可靠性：

1. **提高成功率**：通过智能修复，显著提高案件处理成功率
2. **减少人工干预**：自动修复减少了人工处理失败案例的工作量
3. **保持质量**：修复后的结果质量有保障，符合原始需求
4. **用户友好**：简单的配置界面，灵活的参数设置
5. **系统稳定**：优雅的错误处理，避免系统崩溃

这个功能使得整个多案件处理系统更加智能化和自动化！
