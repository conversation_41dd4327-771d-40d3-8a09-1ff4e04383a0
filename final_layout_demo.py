#!/usr/bin/env python3
"""
最终布局演示 - HTML报告显示
"""

import streamlit as st

def main():
    st.set_page_config(
        page_title="HTML报告布局演示",
        page_icon="📋",
        layout="wide"
    )
    
    # 添加自定义CSS样式
    st.markdown("""
    <style>
    /* 自定义按钮样式 - 蓝色主题 */
    .stButton > button {
        background-color: #1f77b4 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        text-align: left !important;
        justify-content: flex-start !important;
        padding-left: 12px !important;
        font-family: monospace !important;
    }
    
    .stButton > button:hover {
        background-color: #1565c0 !important;
        box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3) !important;
        transform: translateY(-1px) !important;
    }
    
    .stDownloadButton > button {
        background-color: #1976d2 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
    }
    
    .stDownloadButton > button:hover {
        background-color: #1565c0 !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.title("📋 HTML报告布局演示")
    st.write("展示新的左对齐布局和展开/收起功能")
    
    st.markdown("---")
    st.subheader("📄 HTML报告列表")
    
    # 模拟报告数据
    reports = {
        "A001": {
            "case_name": "走私香烟案",
            "html_content": """
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h1 style="color: #2c3e50;">A001:走私香烟案件分析报告</h1>
                <h2 style="color: #34495e;">📁 案件信息</h2>
                <div style="background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p><strong>案件编号:</strong> A001</p>
                    <p><strong>案件名称:</strong> 走私香烟案</p>
                    <p><strong>承办单位:</strong> 深圳海关</p>
                    <p><strong>录入时间:</strong> 2024-01-15 10:30:00</p>
                </div>
                
                <h2 style="color: #34495e;">👥 案件人员信息</h2>
                <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                    <tr style="background-color: #3498db; color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px;">姓名/代号</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">性别</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">角色</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">组织层级</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">张某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">男</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">主犯/组织者</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">核心层</td>
                    </tr>
                    <tr style="background-color: #f2f2f2;">
                        <td style="border: 1px solid #ddd; padding: 8px;">李某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">男</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">运输负责人</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">执行层</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">王某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">男</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">销售负责人</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">执行层</td>
                    </tr>
                </table>
                
                <div style="text-align: right; color: #7f8c8d; font-size: 0.9em; margin-top: 30px;">
                    报告生成时间: 2024-01-15 14:30:00
                </div>
            </div>
            """
        },
        "A002": {
            "case_name": "走私电子产品案",
            "html_content": """
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h1 style="color: #2c3e50;">A002:走私电子产品案件分析报告</h1>
                <h2 style="color: #34495e;">📁 案件信息</h2>
                <div style="background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p><strong>案件编号:</strong> A002</p>
                    <p><strong>案件名称:</strong> 走私电子产品案</p>
                    <p><strong>承办单位:</strong> 广州海关</p>
                    <p><strong>录入时间:</strong> 2024-01-16 09:15:00</p>
                </div>
                
                <h2 style="color: #34495e;">📊 分析过程</h2>
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <p>经过深入分析，该案件涉及利用职务便利进行走私活动。</p>
                    <p>查明走私电子产品价值800万元，涉案资产已被查封。</p>
                    <p>犯罪嫌疑人赵某某构成走私普通货物罪。</p>
                </div>
                
                <h2 style="color: #34495e;">👥 案件人员信息</h2>
                <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                    <tr style="background-color: #3498db; color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px;">姓名/代号</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">角色</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">司法处置结果</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">赵某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">主犯</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">已被查获</td>
                    </tr>
                </table>
                
                <div style="text-align: right; color: #7f8c8d; font-size: 0.9em; margin-top: 30px;">
                    报告生成时间: 2024-01-16 15:45:00
                </div>
            </div>
            """
        },
        "A003": {
            "case_name": "走私奢侈品案件分析",
            "html_content": """
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h1 style="color: #2c3e50;">A003:走私奢侈品案件分析报告</h1>
                <h2 style="color: #34495e;">📁 案件信息</h2>
                <div style="background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p><strong>案件编号:</strong> A003</p>
                    <p><strong>案件名称:</strong> 走私奢侈品案件分析</p>
                    <p><strong>承办单位:</strong> 珠海海关</p>
                    <p><strong>录入时间:</strong> 2024-01-17 14:20:00</p>
                </div>
                
                <h2 style="color: #34495e;">🔗 案件人员关系图</h2>
                <div style="text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 5px; margin: 20px 0;">
                    <p style="color: #7f8c8d; font-style: italic;">
                        [这里会显示人物关系图]<br>
                        犯罪团伙以陈某某为首，专门从事奢侈品走私活动
                    </p>
                </div>
                
                <div style="text-align: right; color: #7f8c8d; font-size: 0.9em; margin-top: 30px;">
                    报告生成时间: 2024-01-17 16:30:00
                </div>
            </div>
            """
        }
    }
    
    # 显示报告列表
    st.write("**单个报告操作:**")
    
    for case_id, report_data in reports.items():
        case_name = report_data["case_name"]
        html_content = report_data["html_content"]
        
        # 报告标题行，包含文本、详细内容按钮和下载按钮
        col1, col2, col3 = st.columns([3, 1, 1])

        with col1:
            # 报告标题文本（纯文本，不是按钮）
            st.markdown(f"**📄 {case_id}: {case_name}**")

        with col2:
            # 详细内容按钮
            is_expanded = st.session_state.get(f"show_report_{case_id}", False)

            if st.button(
                "详细内容",
                key=f"toggle_report_{case_id}",
                use_container_width=True,
                help="查看/隐藏详细报告内容"
            ):
                # 切换显示状态
                st.session_state[f"show_report_{case_id}"] = not is_expanded
                st.rerun()

        with col3:
            # 下载报告按钮
            st.download_button(
                label="📥 下载",
                data=html_content,
                file_name=f"{case_id}.html",
                mime="text/html",
                key=f"download_report_{case_id}",
                use_container_width=True
            )
        
        # 显示HTML报告内容（展开时）
        if st.session_state.get(f"show_report_{case_id}", False):
            st.markdown("---")
            st.markdown(f"**📋 {case_id} 详细报告:**")
            
            # 使用st.components.v1.html正常显示HTML内容
            st.components.v1.html(
                html_content,
                height=600,
                scrolling=True
            )
            
            st.markdown("---")
    
    # 说明文档
    st.markdown("---")
    st.subheader("📖 布局说明")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **✅ 布局特点:**
        - 报告标题为纯文本显示
        - "详细内容"按钮查看报告
        - 3列布局：标题 | 详细内容 | 下载
        - 下载按钮在同一行末尾
        - 蓝色按钮主题统一
        """)

    with col2:
        st.markdown("""
        **🎯 操作说明:**
        - 查看报告标题了解案件信息
        - 点击"详细内容"按钮展开/收起报告
        - 查看完整的HTML报告内容
        - 点击"📥 下载"下载HTML文件
        - HTML内容正常显示样式和表格
        """)

if __name__ == "__main__":
    main()
