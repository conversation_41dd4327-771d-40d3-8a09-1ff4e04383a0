#!/usr/bin/env python3
"""
前端布局改进演示
"""

import streamlit as st

def main():
    st.set_page_config(
        page_title="布局改进演示",
        page_icon="🎨",
        layout="wide"
    )
    
    st.title("🎨 前端布局改进演示")
    st.write("展示新的分析需求区域布局")
    
    # 模拟新的布局
    st.markdown("## 📊 新布局效果")
    
    # 文件上传区域
    uploaded_file = st.file_uploader(
        "选择案件数据文件",
        type=['xlsx', 'xls', 'csv'],
        help="支持Excel文件(.xlsx/.xls)和CSV文件(.csv)，必须包含案件编号字段"
    )
    
    # 分析需求区域（单独一行）
    st.markdown("---")
    st.markdown("### 📋 分析需求配置")
    
    default_analysis_requirements = """任务目标：
1. 组织架构解析（主犯/从犯认定、分工逻辑）
2. 结构化数据提取（CSV格式，14列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的组织和所有提及到的人物信息：实体类型（人员或组织等），姓名/代号，性别，年龄，身份证号，户籍地/现居地，文化程度，直接上级，所属组织，组织层级，分工角色,关联工具/行为,司法处置结果,经济收益（元）

"年龄"要素提取要求：
其中"年龄" 要素，优先从案件内容中分析获取，若无法直接获得"年龄"要素，则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄" 

输出CSV格式（14列）：
实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织之间的多层级关系，生成Mermaid格式的关系图代码。"""

    user_requirements = st.text_area(
        "分析需求",
        value=default_analysis_requirements,
        height=300,
        help="请输入具体的分析需求和任务目标，可以根据实际需要修改分析步骤、输出格式等"
    )
    
    # 对比说明
    st.markdown("---")
    st.markdown("## 📈 布局对比")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ❌ 旧布局")
        st.markdown("""
        **问题：**
        - 分析需求区域太窄（33.3%宽度）
        - 文本难以阅读和编辑
        - 水平空间利用不充分
        - 在小屏幕上体验差
        
        **布局：**
        ```
        ┌─────────────┬─────┐
        │ 文件上传    │需求 │
        │ (66.7%)     │(33%)│
        └─────────────┴─────┘
        ```
        """)
    
    with col2:
        st.markdown("### ✅ 新布局")
        st.markdown("""
        **优势：**
        - 分析需求区域全宽度（100%）
        - 文本清晰易读，编辑方便
        - 充分利用屏幕空间
        - 响应式设计，适配各种设备
        
        **布局：**
        ```
        ┌─────────────────────┐
        │ 文件上传 (100%)     │
        ├─────────────────────┤
        │ 分析需求 (100%)     │
        └─────────────────────┘
        ```
        """)
    
    # 功能特点
    st.markdown("---")
    st.markdown("## ✨ 改进特点")
    
    features = [
        {
            "icon": "📏",
            "title": "全宽度利用",
            "description": "分析需求区域使用100%宽度，充分利用屏幕空间"
        },
        {
            "icon": "📱",
            "title": "响应式设计",
            "description": "垂直布局适应各种屏幕尺寸，移动设备友好"
        },
        {
            "icon": "🎯",
            "title": "清晰分区",
            "description": "使用分隔线和标题明确区分功能区域"
        },
        {
            "icon": "✏️",
            "title": "编辑友好",
            "description": "更宽的文本区域，便于查看和编辑长文本"
        },
        {
            "icon": "🔍",
            "title": "可读性强",
            "description": "文本不再被挤压，阅读体验大幅提升"
        },
        {
            "icon": "♿",
            "title": "可访问性",
            "description": "语义化标题和逻辑顺序，提升可访问性"
        }
    ]
    
    # 显示特点
    for i in range(0, len(features), 2):
        cols = st.columns(2)
        for j, col in enumerate(cols):
            if i + j < len(features):
                feature = features[i + j]
                with col:
                    st.markdown(f"""
                    **{feature['icon']} {feature['title']}**
                    
                    {feature['description']}
                    """)
    
    # 使用指南
    st.markdown("---")
    st.markdown("## 📖 使用指南")
    
    st.markdown("""
    ### 🚀 如何使用新布局
    
    1. **文件上传**
       - 在顶部区域选择案件数据文件
       - 支持Excel和CSV格式
    
    2. **配置分析需求**
       - 在下方的"📋 分析需求配置"区域
       - 查看和编辑分析需求
       - 可以根据实际需要修改内容
    
    3. **开始分析**
       - 配置完成后开始案件分析
       - 系统会根据您的需求进行处理
    
    ### 💡 编辑技巧
    
    - **全选文本**: Ctrl+A (Windows) 或 Cmd+A (Mac)
    - **查找替换**: Ctrl+F (Windows) 或 Cmd+F (Mac)
    - **多行编辑**: 支持复制粘贴大段文本
    - **实时预览**: 修改后立即生效
    """)

if __name__ == "__main__":
    main()
