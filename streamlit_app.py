import streamlit as st
import asyncio
import pandas as pd
import tempfile
import os
from datetime import datetime
from pathlib import Path
import base64
import json
import time
from typing import Dict, List, Any
import uuid

# 导入智能体模块
from multi_agents import MultiCaseAnalysisOrchestrator, generate_session_id

# 页面配置
st.set_page_config(
    page_title="多案件信息提取分析助手",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS - 修复长条框问题
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .step-container {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
        background-color: #f8f9fa;
    }
    
    .step-header {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .info-box {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .workflow-step {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 0 5px 5px 0;
    }
    
    .workflow-step.success {
        border-left-color: #28a745;
        background-color: #d4edda;
    }
    
    .workflow-step.error {
        border-left-color: #dc3545;
        background-color: #f8d7da;
    }
    
    .file-info-card {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    /* 优化1: 增大源文件内容的字体 */
    .source-content {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
        max-height: 400px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 1.0em;  /* 从0.9em增大到1.0em */
        line-height: 1.5;  /* 从1.4增大到1.5 */
    }
    
    /* 优化2: 文件详情单行显示样式 */
    .file-details-inline {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        align-items: center;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .file-detail-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .file-detail-label {
        font-weight: bold;
        color: #2c3e50;
    }
    
    .file-detail-value {
        color: #495057;
    }
    
    /* 优化3: 分析过程内容格式化 */
    .analysis-content {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 0.95em;
        line-height: 1.6;
        color: #333;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    
    .report-container {
        border: 2px solid #28a745;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
        background-color: #f8fff9;
    }
    
    .data-edit-notice {
        background-color: #fff3cd;
        border: 1px solid #ffd60a;
        border-radius: 5px;
        padding: 10px;
        margin: 10px 0;
        color: #856404;
    }

    .progress-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }

    .progress-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .progress-item:last-child {
        border-bottom: none;
    }

    .progress-status {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
    }

    .status-processing {
        background-color: #ffc107;
        animation: pulse 1.5s infinite;
    }

    .status-completed {
        background-color: #28a745;
    }

    .status-failed {
        background-color: #dc3545;
    }

    .status-pending {
        background-color: #6c757d;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* 关系图画廊滚动容器 - 增强大框效果 */
    .relationship-gallery-container {
        border: 3px solid #3498db;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        box-shadow: 0 8px 16px rgba(52, 152, 219, 0.2),
                    inset 0 1px 3px rgba(255, 255, 255, 0.8);
        position: relative;
    }

    .relationship-gallery-container::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg, #3498db, #2980b9, #3498db);
        border-radius: 18px;
        z-index: -1;
        animation: borderGlow 3s ease-in-out infinite alternate;
    }

    @keyframes borderGlow {
        0% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .relationship-gallery-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1rem 1.5rem;
        margin: -1.5rem -1.5rem 1.5rem -1.5rem;
        border-radius: 12px 12px 0 0;
        font-weight: bold;
        font-size: 1.2em;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .relationship-gallery-scroll {
        max-height: 800px;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 1rem;
        border-radius: 10px;
        background-color: white;
        border: 2px solid #e9ecef;
        box-shadow: inset 0 2px 8px rgba(0,0,0,0.05);
    }

    /* 增强的自定义滚动条样式 */
    .relationship-gallery-scroll::-webkit-scrollbar {
        width: 12px;
    }

    .relationship-gallery-scroll::-webkit-scrollbar-track {
        background: linear-gradient(180deg, #f8f9fa, #e9ecef);
        border-radius: 6px;
        border: 1px solid #dee2e6;
    }

    .relationship-gallery-scroll::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, #3498db, #2980b9);
        border-radius: 6px;
        border: 1px solid #2980b9;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .relationship-gallery-scroll::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, #2980b9, #1f5f8b);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .relationship-gallery-scroll::-webkit-scrollbar-corner {
        background: #f8f9fa;
    }

    .case-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin: 0;
        padding: 1rem;
        position: relative;
    }

    .case-item {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .case-image-container {
        position: relative;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .case-image-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    .case-image-display {
        width: 100%;
        height: auto;
        max-height: 400px;
        object-fit: contain;
        display: block;
    }

    .image-caption {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 0.5rem 1rem;
        text-align: center;
        font-size: 0.9em;
        font-weight: 500;
    }

    /* 确保Streamlit组件在滚动容器内正确显示 */
    .relationship-gallery-scroll .stImage {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .relationship-gallery-scroll .stDownloadButton {
        margin-bottom: 1rem;
    }

    .relationship-gallery-scroll .stDownloadButton > button {
        width: 100%;
        background-color: #28a745 !important;
        border-color: #28a745 !important;
    }

    .relationship-gallery-scroll .stDownloadButton > button:hover {
        background-color: #218838 !important;
        border-color: #1e7e34 !important;
    }

    /* 画廊标题样式 */
    .gallery-title-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1rem 1.5rem;
        margin: 2rem 0 0 0;
        border-radius: 15px 15px 0 0;
        font-weight: bold;
        font-size: 1.2em;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        border: 3px solid #3498db;
        border-bottom: none;
    }

    /* 使用CSS选择器为包含gallery-content-marker的容器添加大框样式 */
    .gallery-content-marker {
        display: none;
    }

    /* 选择包含gallery-content-marker的容器的父容器 */
    .stContainer:has(.gallery-content-marker) {
        border: 3px solid #3498db !important;
        border-top: none !important;
        border-radius: 0 0 15px 15px !important;
        padding: 1.5rem !important;
        margin: 0 0 2rem 0 !important;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef) !important;
        box-shadow: 0 8px 16px rgba(52, 152, 219, 0.2) !important;
        max-height: 800px !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }

    /* 如果浏览器不支持:has选择器，使用备用方案 */
    .gallery-content-marker + * {
        border: 3px solid #3498db;
        border-top: none;
        border-radius: 0 0 15px 15px;
        padding: 1.5rem;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        box-shadow: 0 8px 16px rgba(52, 152, 219, 0.2);
        max-height: 800px;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .item-spacer {
        height: 1rem;
    }




    .case-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .case-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .case-card h4 {
        margin-top: 0;
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.5rem;
    }

    .case-image {
        width: 100%;
        max-height: 200px;
        object-fit: contain;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin: 0.5rem 0;
    }

    /* 关系图统计信息 */
    .gallery-stats {
        background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
        border: 1px solid #bee5eb;
        border-radius: 8px;
        padding: 0.8rem;
        margin: 0.5rem 0;
        text-align: center;
        font-size: 0.9em;
        color: #0c5460;
    }

    .gallery-stats strong {
        color: #2980b9;
    }

    /* 空状态样式 */
    .empty-gallery {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 2px dashed #dee2e6;
        margin: 1rem 0;
    }

    .empty-gallery i {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    /* 移除Streamlit默认的padding和margin */
    .element-container {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 移除空白的div元素 */
    .stMarkdown div:empty {
        display: none !important;
    }

    /* 确保容器紧凑 */
    .block-container {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    /* 自定义按钮样式 - 蓝色主题 */
    .stButton > button {
        background-color: #1f77b4 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .stButton > button:hover {
        background-color: #1565c0 !important;
        box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3) !important;
        transform: translateY(-1px) !important;
    }

    .stButton > button:active {
        background-color: #0d47a1 !important;
        transform: translateY(0px) !important;
    }

    /* 主要按钮样式 */
    .stButton > button[kind="primary"] {
        background-color: #2196f3 !important;
        color: white !important;
    }

    .stButton > button[kind="primary"]:hover {
        background-color: #1976d2 !important;
    }

    /* 下载按钮样式 */
    .stDownloadButton > button {
        background-color: #1976d2 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
    }

    .stDownloadButton > button:hover {
        background-color: #1565c0 !important;
    }

    /* 详细内容按钮样式 */
    button[data-testid="baseButton-secondary"] {
        background-color: #1f77b4 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        text-align: center !important;
        font-size: 14px !important;
    }

    button[data-testid="baseButton-secondary"]:hover {
        background-color: #1565c0 !important;
        box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3) !important;
        transform: translateY(-1px) !important;
    }
</style>
""", unsafe_allow_html=True)

# 初始化session state
def init_session_state():
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = generate_session_id()
    if 'orchestrator' not in st.session_state:
        st.session_state.orchestrator = MultiCaseAnalysisOrchestrator(
            max_concurrent=st.session_state.get('max_concurrent', 10),
            max_repair_attempts=st.session_state.get('max_repair_attempts', 2)
        )
    if 'session_history' not in st.session_state:
        st.session_state.session_history = {}
    if 'current_batch_data' not in st.session_state:
        st.session_state.current_batch_data = None
    if 'processing' not in st.session_state:
        st.session_state.processing = False
    if 'workflow_status' not in st.session_state:
        st.session_state.workflow_status = []
    if 'processing_progress' not in st.session_state:
        st.session_state.processing_progress = {}
    if 'relationship_images' not in st.session_state:
        st.session_state.relationship_images = {}
    if 'show_reports' not in st.session_state:
        st.session_state.show_reports = False
    if 'generated_reports' not in st.session_state:
        st.session_state.generated_reports = {}
    if 'data_changed' not in st.session_state:
        st.session_state.data_changed = False
    if 'max_concurrent' not in st.session_state:
        st.session_state.max_concurrent = 10
    if 'max_repair_attempts' not in st.session_state:
        st.session_state.max_repair_attempts = 2
    if 'file_processed' not in st.session_state:
        st.session_state.file_processed = False

def display_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.title("📊 多案件分析助手")

        # 当前会话信息
        st.write(f"**会话ID**: `{st.session_state.current_session_id[:8]}...`")
        st.write(f"**时间**: {datetime.now().strftime('%H:%M:%S')}")

        st.divider()

        # 并发设置
        st.subheader("⚙️ 处理设置")
        new_max_concurrent = st.slider(
            "最大并发数",
            min_value=1,
            max_value=20,
            value=st.session_state.max_concurrent,
            help="同时处理的案件数量，建议根据系统性能调整"
        )
        if new_max_concurrent != st.session_state.max_concurrent:
            st.session_state.max_concurrent = new_max_concurrent
            # 重新初始化orchestrator
            st.session_state.orchestrator = MultiCaseAnalysisOrchestrator(
                max_concurrent=new_max_concurrent,
                max_repair_attempts=st.session_state.max_repair_attempts
            )
            st.rerun()

        new_max_repair_attempts = st.slider(
            "每个案件最大修复次数",
            min_value=0,
            max_value=5,
            value=st.session_state.max_repair_attempts,
            help="当案件处理失败时，修复智能体的最大重试次数"
        )
        if new_max_repair_attempts != st.session_state.max_repair_attempts:
            st.session_state.max_repair_attempts = new_max_repair_attempts
            # 重新初始化orchestrator
            st.session_state.orchestrator = MultiCaseAnalysisOrchestrator(
                max_concurrent=st.session_state.max_concurrent,
                max_repair_attempts=new_max_repair_attempts
            )
            st.rerun()

        st.divider()

        # 新建会话
        if st.button("🆕 新建会话", type="primary", use_container_width=True):
            # 保存当前会话
            if st.session_state.current_batch_data:
                st.session_state.session_history[st.session_state.current_session_id] = {
                    "batch_data": st.session_state.current_batch_data,
                    "timestamp": datetime.now().isoformat()
                }

            # 重置会话
            st.session_state.current_session_id = generate_session_id()
            st.session_state.current_batch_data = None
            st.session_state.workflow_status = []
            st.session_state.processing_progress = {}
            st.session_state.relationship_images = {}
            st.session_state.show_reports = False
            st.session_state.generated_reports = {}
            st.session_state.data_changed = False
            st.session_state.file_processed = False
            st.rerun()

        # 历史会话
        if st.session_state.session_history:
            st.subheader("📚 历史会话")
            for session_id, session_data in list(st.session_state.session_history.items())[-5:]:
                batch_info = session_data.get("batch_data", {})
                case_count = batch_info.get("total_cases", 0)
                timestamp = session_data["timestamp"][:16]

                if st.button(f"批次({case_count}个案件)\n{timestamp}", key=f"history_{session_id}", use_container_width=True):
                    st.session_state.current_session_id = session_id
                    st.session_state.current_batch_data = session_data["batch_data"]
                    st.session_state.show_reports = False
                    st.session_state.generated_reports = {}
                    st.session_state.data_changed = False
                    st.rerun()
        
        st.divider()
        
        # 使用指南
        with st.expander("💡 使用指南"):
            st.markdown("""
            **操作流程：**
            1. 📁 上传案件数据文件（xlsx/xls/csv）
            2. 📝 输入分析需求
            3. 🔍 自动预处理数据（去重、合并）
            4. ⚡ 并发提取多个案件要素
            5. 🖼️ 为每个案件生成关系图
            6. ✏️ 编辑表格数据（可选）
            7. 💾 批量导入数据库（仅一次）
            8. 📋 批量生成分析报告

            **文件格式要求：**
            - 支持 .xlsx、.xls、.csv 格式
            - 必须包含"案件编号"字段
            - 建议包含"案件名称"、"承办单位"等字段
            - 内容字段：正文内容、到案情况、依法侦查查明等

            **并发处理：**
            - 支持最大20个案件同时处理
            - 实时显示处理进度
            - 自动处理失败重试
            - 支持部分成功结果

            **数据管理：**
            - 自动数据去重和合并
            - 支持数据实时编辑
            - 每个案件独立的关系图
            - 批量报告生成和下载

            **文件管理：**
            - 每个会话独立目录
            - 自动保存所有处理结果
            - 支持历史会话回溯
            - 按案件编号命名输出文件
            """)
        
        # 当前工作流状态
        if st.session_state.workflow_status:
            st.subheader("🔄 工作流状态")
            for step in st.session_state.workflow_status:
                status_icon = "✅" if step["status"] == "success" else "❌" if step["status"] == "error" else "🔄"
                st.write(f"{status_icon} {step['name']}")

def display_file_upload_section():
    """显示文件上传区域"""
    with st.container():
        # 分析需求区域（优先显示）
        st.markdown("### 📋 分析需求")

        # 用户需求字段定义
        user_requirements = st.text_area(
            "",  # 移除小字标签
            value="实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,分工角色,关联人物,关联关系,关联工具/物品,关联行为,关联场所,司法处置结果,经济收益（元）",
            height=100,
            help="请输入需要提取的字段，用逗号分隔"
        )

        # 处理用户需求：替换中文逗号为英文逗号
        user_requirements_new = user_requirements.replace("，", ",")

        # 统计标签数量
        user_requirements_count = len([field.strip() for field in user_requirements_new.split(",") if field.strip()])

        # 显示处理后的信息
        st.info(f"处理后的字段: {user_requirements_new}")
        st.info(f"字段数量: {user_requirements_count}")

        # 分析需求模板
        analysis_requirements = f"""
任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{user_requirements_count}列）：
{user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。"""

        # 显示分析需求模板
        with st.expander("📋 查看生成的分析需求模板", expanded=False):
            st.text_area("分析需求模板", value=analysis_requirements, height=400, disabled=True)

        # 文件上传区域（步骤1）
        st.markdown("---")
        st.markdown("### 📁 步骤1: 上传案件数据文件")
        uploaded_file = st.file_uploader(
            "",  # 移除重复标题
            type=['xlsx', 'xls', 'csv'],
            help="支持Excel文件(.xlsx/.xls)和CSV文件(.csv)，必须包含案件编号字段"
        )

        if uploaded_file is not None:
            st.success(f"✅ 已上传文件: {uploaded_file.name}")

            # 显示文件信息
            file_size = len(uploaded_file.getvalue())
            file_type = uploaded_file.name.split('.')[-1].upper()

            st.markdown(f"""
            <div class="file-info-card">
                <h4>📄 文件信息</h4>
                <p><strong>文件名:</strong> {uploaded_file.name}</p>
                <p><strong>文件类型:</strong> {file_type}</p>
                <p><strong>文件大小:</strong> {file_size:,} 字节</p>
            </div>
            """, unsafe_allow_html=True)

            # 预览数据
            if not st.session_state.file_processed:
                try:
                    # 读取文件预览
                    if file_type in ['XLSX', 'XLS']:
                        df_preview = pd.read_excel(uploaded_file, nrows=5)
                    else:  # CSV
                        df_preview = pd.read_csv(uploaded_file, nrows=5, encoding='utf-8')

                    st.write("**数据预览（前5行）：**")
                    st.dataframe(df_preview, use_container_width=True)

                    # 检查必要字段
                    required_fields = ['案件编号']
                    missing_fields = [field for field in required_fields if field not in df_preview.columns]

                    if missing_fields:
                        st.error(f"❌ 缺少必要字段: {', '.join(missing_fields)}")
                        return None, None
                    else:
                        st.success("✅ 数据格式检查通过")

                except Exception as e:
                    st.error(f"❌ 文件读取失败: {str(e)}")
                    return None, None

            if st.button("🚀 开始处理", type="primary", disabled=st.session_state.processing):
                return uploaded_file, analysis_requirements

    return None, None

def display_processing_progress():
    """显示处理进度"""
    if st.session_state.processing_progress:
        st.markdown('<div class="step-header">⚡ 处理进度</div>', unsafe_allow_html=True)

        progress_container = st.container()
        with progress_container:
            st.markdown('<div class="progress-container">', unsafe_allow_html=True)

            total_cases = len(st.session_state.processing_progress)
            completed_cases = sum(1 for status in st.session_state.processing_progress.values() if status.get("status") == "completed")
            failed_cases = sum(1 for status in st.session_state.processing_progress.values() if status.get("status") == "failed")
            processing_cases = sum(1 for status in st.session_state.processing_progress.values() if status.get("status") == "processing")

            # 总体进度条
            progress_percentage = (completed_cases + failed_cases) / total_cases if total_cases > 0 else 0
            st.progress(progress_percentage)
            st.write(f"总进度: {completed_cases + failed_cases}/{total_cases} ({progress_percentage:.1%})")

            # 状态统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("总案件数", total_cases)
            with col2:
                st.metric("已完成", completed_cases, delta=None)
            with col3:
                st.metric("处理中", processing_cases, delta=None)
            with col4:
                st.metric("失败", failed_cases, delta=None)

            # 详细进度列表
            for case_index, progress_info in st.session_state.processing_progress.items():
                status = progress_info.get("status", "pending")
                message = progress_info.get("message", "")

                status_class = f"status-{status}"
                status_icon = {
                    "pending": "⏳",
                    "processing": "🔄",
                    "completed": "✅",
                    "failed": "❌"
                }.get(status, "⏳")

                st.markdown(f'''
                <div class="progress-item">
                    <div class="progress-status {status_class}">{status_icon}</div>
                    <div>案件 {case_index + 1}: {message}</div>
                </div>
                ''', unsafe_allow_html=True)

            st.markdown('</div>', unsafe_allow_html=True)

async def update_progress(case_index: int, status: str, message: str):
    """更新处理进度的回调函数"""
    if 'processing_progress' not in st.session_state:
        st.session_state.processing_progress = {}

    st.session_state.processing_progress[case_index] = {
        "status": status,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }

def display_batch_results(batch_data: Dict[str, Any]):
    """显示批量处理结果"""
    with st.container():
        st.markdown('<div class="step-header">📊 步骤2: 批量处理结果</div>', unsafe_allow_html=True)

        if batch_data["status"] == "success":
            st.markdown('<div class="success-box">✅ 批量处理完成</div>', unsafe_allow_html=True)

            # 显示处理统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("批次号", batch_data.get("batch_id", ""))
            with col2:
                st.metric("总案件数", batch_data.get("total_cases", 0))
            with col3:
                st.metric("成功处理", batch_data.get("successful_cases", 0))
            with col4:
                st.metric("处理失败", batch_data.get("failed_cases", 0))

            # 显示文件信息
            file_info = batch_data.get("file_info", {})
            if file_info:
                st.markdown(f"""
                <div class="file-info-card">
                    <h4>📄 源文件信息</h4>
                    <p><strong>文件名:</strong> {file_info.get('original_name', 'N/A')}</p>
                    <p><strong>文件大小:</strong> {file_info.get('file_size', 0):,} 字节</p>
                    <p><strong>原始行数:</strong> {file_info.get('total_rows', 0):,}</p>
                    <p><strong>列数:</strong> {file_info.get('total_columns', 0)}</p>
                </div>
                """, unsafe_allow_html=True)

            # 显示预处理信息
            preprocessing = batch_data.get("preprocessing", {})
            if preprocessing:
                st.write("**数据预处理结果:**")
                st.info(preprocessing.get("processing_summary", ""))

                # 显示预处理后的案件列表
                processed_data = preprocessing.get("processed_data", [])
                if processed_data:
                    with st.expander("📋 预处理后的案件列表", expanded=False):
                        # 构建案件列表数据表格
                        case_table_data = []
                        for case in processed_data:
                            case_id = case.get('案件编号', 'N/A')
                            case_name = case.get('案件名称', 'N/A')
                            host_org = case.get('承办单位', 'N/A')
                            case_content = case.get('案件内容', 'N/A')
                            rec_time = case.get('录入时间', 'N/A')

                            # 保持案件内容完整，不截断
                            case_table_data.append({
                                '案件编号': case_id,
                                '案件名称': case_name,
                                '承办单位': host_org,
                                '录入时间': rec_time,
                                '案件内容': case_content
                            })

                        if case_table_data:
                            case_df = pd.DataFrame(case_table_data)
                            st.dataframe(
                                case_df,
                                use_container_width=True,
                                hide_index=True,
                                height=400,  # 设置表格高度以便滚动查看
                                column_config={
                                    '案件编号': st.column_config.TextColumn('案件编号', width="small"),
                                    '案件名称': st.column_config.TextColumn('案件名称', width="medium"),
                                    '承办单位': st.column_config.TextColumn('承办单位', width="medium"),
                                    '录入时间': st.column_config.TextColumn('录入时间', width="medium"),
                                    '案件内容': st.column_config.TextColumn('案件内容', width="large")
                                }
                            )

            # 显示合并的CSV数据
            merged_csv = batch_data.get("merged_csv_data", "")
            if merged_csv:
                st.subheader("📋 提取的案件数据")

                # 转换为DataFrame显示
                lines = merged_csv.strip().split('\n')
                if len(lines) > 1:
                    headers = [h.strip() for h in lines[0].split(',')]
                    data_rows = []
                    for line in lines[1:]:
                        if line.strip():
                            row = [c.strip() for c in line.split(',')]
                            # 确保行长度与表头一致
                            while len(row) < len(headers):
                                row.append("")
                            data_rows.append(row[:len(headers)])

                    if data_rows:
                        df = pd.DataFrame(data_rows, columns=headers)

                        # 定义字段名称映射
                        field_name_mapping = {
                            'batch_id': '批次号',
                            'host_org': '承办单位',
                            'case_id': '案件编号',
                            'case_name': '案件名称'
                        }

                        # 可编辑的数据表格
                        st.write("**可编辑数据表格** (编辑后请点击\"保存编辑\"按钮):")

                        # 创建列配置，为特定字段使用中文显示名称
                        column_config = {}
                        for col in df.columns:
                            display_name = field_name_mapping.get(col, col)
                            column_config[col] = st.column_config.TextColumn(
                                display_name,
                                width="medium",
                                help=f"编辑{display_name}"
                            )

                        edited_df = st.data_editor(
                            df,
                            use_container_width=True,
                            num_rows="dynamic",
                            key=f"batch_data_editor_{st.session_state.current_session_id}",
                            column_config=column_config
                        )

                        # 保存编辑按钮
                        col1, col2 = st.columns([1, 3])
                        with col1:
                            if st.button("💾 保存编辑", use_container_width=True):
                                # 更新批次数据中的CSV
                                updated_csv = edited_df.to_csv(index=False, header=True)
                                st.session_state.current_batch_data["merged_csv_data"] = updated_csv
                                st.session_state.data_changed = True
                                st.success("✅ 数据已保存")
                                st.rerun()

                        # 下载CSV
                        csv_download = edited_df.to_csv(index=False)
                        with col2:
                            st.download_button(
                                label="📥 下载CSV数据",
                                data=csv_download,
                                file_name=f"batch_{batch_data.get('batch_id', 'data')}.csv",
                                mime="text/csv",
                                use_container_width=True
                            )

            # 显示失败的案件
            extraction = batch_data.get("extraction", {})
            failed_cases = extraction.get("failed_cases", [])
            if failed_cases:
                with st.expander(f"❌ 处理失败的案件 ({len(failed_cases)}个)", expanded=False):
                    # 构建失败案件数据表格
                    failed_table_data = []

                    # 获取原始案件数据用于补充信息
                    preprocessing = batch_data.get("preprocessing", {})
                    processed_data = preprocessing.get("processed_data", [])
                    case_data_map = {case.get('案件编号', ''): case for case in processed_data}

                    for failed_case in failed_cases:
                        case_id = failed_case.get('case_id', 'N/A')
                        error_msg = failed_case.get('error', '未知错误')

                        # 从原始数据中获取案件信息
                        original_case = case_data_map.get(case_id, {})
                        case_name = original_case.get('案件名称', 'N/A')
                        host_org = original_case.get('承办单位', 'N/A')
                        case_content = original_case.get('案件内容', 'N/A')
                        rec_time = original_case.get('录入时间', 'N/A')

                        # 保持完整内容，不截断

                        failed_table_data.append({
                            '案件编号': case_id,
                            '案件名称': case_name,
                            '承办单位': host_org,
                            '录入时间': rec_time,
                            '案件内容': case_content,
                            '报错原因': error_msg
                        })

                    if failed_table_data:
                        failed_df = pd.DataFrame(failed_table_data)
                        st.dataframe(
                            failed_df,
                            use_container_width=True,
                            hide_index=True,
                            height=300,  # 设置表格高度以便滚动查看
                            column_config={
                                '案件编号': st.column_config.TextColumn('案件编号', width="small"),
                                '案件名称': st.column_config.TextColumn('案件名称', width="medium"),
                                '承办单位': st.column_config.TextColumn('承办单位', width="medium"),
                                '录入时间': st.column_config.TextColumn('录入时间', width="medium"),
                                '案件内容': st.column_config.TextColumn('案件内容', width="large"),
                                '报错原因': st.column_config.TextColumn('报错原因', width="large")
                            }
                        )

            # 保存到session state
            st.session_state.current_batch_data = batch_data

        else:
            st.markdown(f'<div class="error-box">❌ 批量处理失败: {batch_data.get("message", "未知错误")}</div>', unsafe_allow_html=True)
            if batch_data.get("error"):
                st.error(f"错误详情: {batch_data['error']}")

def display_relationship_gallery():
    """显示人物关系图画廊"""
    if not st.session_state.current_batch_data:
        return

    relationship_images = st.session_state.current_batch_data.get("relationship_images", {})

    if relationship_images:
        # 显示统计信息
        total_images = len(relationship_images)
        st.markdown(f'''
        <div class="gallery-stats">
            📊 <strong>关系图统计:</strong> 共有 <strong>{total_images}</strong> 个案件的人物关系图
        </div>
        ''', unsafe_allow_html=True)

        # 搜索功能
        search_term = st.text_input("🔍 搜索案件编号或名称", placeholder="输入案件编号或名称进行搜索")

        # 过滤图片
        filtered_images = relationship_images
        if search_term:
            filtered_images = {k: v for k, v in relationship_images.items() if search_term.lower() in k.lower()}

        if filtered_images:
            # 显示过滤后的统计和批量下载
            if search_term:
                st.markdown(f'''
                <div class="gallery-stats">
                    🔍 <strong>搜索结果:</strong> 找到 <strong>{len(filtered_images)}</strong> 个匹配的案件
                </div>
                ''', unsafe_allow_html=True)

            # 批量下载功能
            if len(filtered_images) > 1:
                col1, col2 = st.columns([3, 1])
                with col2:
                    # 创建ZIP文件
                    import zipfile
                    import io

                    zip_buffer = io.BytesIO()
                    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                        for case_id, image_base64 in filtered_images.items():
                            image_data = base64.b64decode(image_base64)
                            zip_file.writestr(f"{case_id}_关系图.png", image_data)

                    zip_buffer.seek(0)
                    st.download_button(
                        label="📦 批量下载所有关系图",
                        data=zip_buffer.getvalue(),
                        file_name=f"关系图批量下载_{len(filtered_images)}个案件.zip",
                        mime="application/zip",
                        use_container_width=True,
                        key="batch_download_all_images"
                    )

            # 使用简单有效的方法：直接用CSS样式包围整个区域
            st.markdown(f'''
            <div style="
                border: 4px solid #3498db;
                border-radius: 15px;
                padding: 0;
                margin: 2rem 0;
                background: linear-gradient(145deg, #f8f9fa, #e9ecef);
                box-shadow: 0 8px 16px rgba(52, 152, 219, 0.3);
                overflow: hidden;
            ">
                <div style="
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                    padding: 1rem 1.5rem;
                    font-weight: bold;
                    font-size: 1.2em;
                    text-align: center;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                ">
                    🖼️ 案件人物关系图画廊 ({len(filtered_images)} 个案件)
                </div>
                <div style="
                    max-height: 800px;
                    overflow-y: auto;
                    overflow-x: hidden;
                    padding: 1.5rem;
                    background: white;
                ">
            ''', unsafe_allow_html=True)

            # 获取案件信息
            extraction = st.session_state.current_batch_data.get("extraction", {})
            individual_results = extraction.get("individual_results", [])

            # 创建网格布局 - 每行2列
            for i in range(0, len(filtered_images), 2):
                cols = st.columns(2)
                items = list(filtered_images.items())[i:i+2]

                for j, (case_id, image_base64) in enumerate(items):
                    if j < len(items):  # 确保不超出范围
                        with cols[j]:
                            # 获取案件详细信息
                            case_name = "未知案件"
                            batch_id = ""
                            host_org = ""
                            for result in individual_results:
                                if result.get("case_id") == case_id:
                                    case_name = result.get("case_name", "未知案件")
                                    batch_id = result.get("batch_id", "")
                                    host_org = result.get("host_org", "")
                                    break

                            # 案件信息卡片 - 显示中文字段名
                            st.markdown(f'''
                            <div class="case-card">
                                <h4>案件编号: {case_id}</h4>
                                <p><strong>案件名称:</strong> {case_name}</p>
                                {f'<p><strong>批次号:</strong> {batch_id}</p>' if batch_id else ''}
                                {f'<p><strong>承办单位:</strong> {host_org}</p>' if host_org else ''}
                            </div>
                            ''', unsafe_allow_html=True)

                            # 显示图片
                            image_data = base64.b64decode(image_base64)
                            st.image(image_data, caption=f"{case_id} 人物关系图", use_container_width=True)

                            # 下载按钮 - 紧跟在每张图片下方
                            st.download_button(
                                label="📥 下载关系图",
                                data=image_data,
                                file_name=f"{case_id}.png",
                                mime="image/png",
                                use_container_width=True,
                                key=f"download_img_{case_id}"
                            )

                            # 添加间距
                            st.markdown("<div style='height: 1rem;'></div>", unsafe_allow_html=True)

            # 结束大框容器
            st.markdown('''
                </div>
            </div>
            ''', unsafe_allow_html=True)
        else:
            # 搜索无结果的情况
            st.markdown(f'''
            <div class="empty-gallery">
                <i>🔍</i>
                <h3>没有找到匹配的关系图</h3>
                <p>搜索词 "<strong>{search_term}</strong>" 没有匹配到任何案件</p>
                <p>请尝试其他关键词或清空搜索框查看所有关系图</p>
            </div>
            ''', unsafe_allow_html=True)
    else:
        # 完全没有关系图的情况
        st.markdown('''
        <div class="empty-gallery">
            <i>📊</i>
            <h3>暂无人物关系图</h3>
            <p>请先完成案件处理，系统将自动生成人物关系图</p>
        </div>
        ''', unsafe_allow_html=True)

def display_database_section():
    """显示数据库操作区域"""
    if not st.session_state.current_batch_data:
        return

    with st.container():
        st.markdown('<div class="step-header">💾 步骤3: 批量导入数据库</div>', unsafe_allow_html=True)

        merged_csv = st.session_state.current_batch_data.get("merged_csv_data", "")
        imported_to_db = st.session_state.current_batch_data.get("imported_to_db", False)
        successful_cases = st.session_state.current_batch_data.get("successful_cases", 0)

        if merged_csv:
            st.write("**准备导入的数据:**")

            # 显示数据编辑状态
            if st.session_state.data_changed:
                st.info("🔄 将使用您编辑后的最新数据进行导入")

            # 显示数据预览
            lines = merged_csv.strip().split('\n')
            if len(lines) > 1:
                st.write(f"共 {len(lines) - 1} 条记录，来自 {successful_cases} 个案件")

            # 检查是否已导入
            if imported_to_db:
                st.warning("⚠️ 此批次数据已经导入过数据库，不能重复导入！")
                st.info("如需重新导入，请重新处理文件或创建新会话。")
            else:
                if st.button("💾 批量导入数据库", type="primary", use_container_width=True):
                    with st.spinner("批量导入数据中..."):
                        try:
                            import_result = asyncio.run(
                                st.session_state.orchestrator.insert_batch_to_database(
                                    st.session_state.current_session_id
                                )
                            )

                            if import_result["status"] == "success":
                                st.success("✅ 批量数据导入成功!")

                                # 显示执行结果
                                total_records = import_result.get("total_records", 0)
                                affected_rows = import_result.get("affected_rows", 0)
                                st.info(f"成功插入 {total_records} 条记录，影响 {affected_rows} 行")

                                # 显示执行的SQL
                                if import_result.get("sql"):
                                    with st.expander("📝 执行的SQL语句"):
                                        st.code(import_result["sql"], language="sql")

                                # 更新工作流状态
                                st.session_state.workflow_status.append({
                                    "name": "批量数据库导入",
                                    "status": "success",
                                    "timestamp": datetime.now().isoformat()
                                })

                                # 更新当前批次数据的导入状态
                                st.session_state.current_batch_data["imported_to_db"] = True

                                # 刷新页面以显示新状态
                                st.rerun()
                            else:
                                st.error(f"❌ 批量数据导入失败: {import_result.get('message', '')}")
                                st.session_state.workflow_status.append({
                                    "name": "批量数据库导入",
                                    "status": "error",
                                    "timestamp": datetime.now().isoformat()
                                })
                        except Exception as e:
                            st.error(f"导入异常: {str(e)}")
        else:
            st.warning("没有可导入的数据")

def display_reports_section():
    """显示报告生成区域"""
    if not st.session_state.current_batch_data:
        return

    with st.container():
        st.markdown('<div class="step-header">📋 步骤4: 批量生成分析报告</div>', unsafe_allow_html=True)

        successful_cases = st.session_state.current_batch_data.get("successful_cases", 0)

        # 显示数据编辑状态提示
        if st.session_state.data_changed:
            st.info("🔄 报告将基于您编辑后的最新数据生成")

        st.write(f"**将为 {successful_cases} 个成功处理的案件生成独立的HTML报告**")

        # 生成报告按钮
        if st.button("📋 批量生成HTML报告", type="primary", use_container_width=True):
            with st.spinner("批量生成报告中..."):
                try:
                    report_result = st.session_state.orchestrator.generate_batch_reports(
                        st.session_state.current_session_id
                    )

                    if report_result["status"] == "success":
                        st.success("✅ 批量报告生成成功!")

                        generated_count = report_result.get("generated_count", 0)
                        reports = report_result.get("reports", {})

                        st.info(f"成功生成 {generated_count} 个报告文件")

                        # 保存报告到session state
                        st.session_state.generated_reports = reports
                        st.session_state.show_reports = True

                        # 更新工作流状态
                        st.session_state.workflow_status.append({
                            "name": "批量报告生成",
                            "status": "success",
                            "timestamp": datetime.now().isoformat()
                        })

                        st.rerun()
                    else:
                        st.error(f"❌ 批量报告生成失败: {report_result.get('message', '')}")
                        st.session_state.workflow_status.append({
                            "name": "批量报告生成",
                            "status": "error",
                            "timestamp": datetime.now().isoformat()
                        })
                except Exception as e:
                    st.error(f"报告生成异常: {str(e)}")

        # 如果报告已生成，显示下载选项
        if st.session_state.show_reports and st.session_state.generated_reports:
            st.markdown('<div class="step-header">📄 生成的分析报告</div>', unsafe_allow_html=True)

            reports = st.session_state.generated_reports

            # 批量下载按钮
            if len(reports) > 1:
                # 创建ZIP文件用于批量下载
                import zipfile
                import io

                zip_buffer = io.BytesIO()
                with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for case_id, report_data in reports.items():
                        if report_data.get("status") == "success":
                            filename = report_data.get("filename", f"{case_id}.html")
                            html_content = report_data.get("html_content", "")
                            zip_file.writestr(filename, html_content.encode('utf-8'))

                zip_buffer.seek(0)

                st.download_button(
                    label="📦 批量下载所有报告 (ZIP)",
                    data=zip_buffer.getvalue(),
                    file_name=f"batch_reports_{st.session_state.current_batch_data.get('batch_id', 'reports')}.zip",
                    mime="application/zip",
                    use_container_width=True
                )

            # 单个报告操作
            st.write("**单个报告操作:**")

            for case_id, report_data in reports.items():
                if report_data.get("status") == "success":
                    case_name = report_data.get("case_name", "未知案件")
                    filename = report_data.get("filename", f"{case_id}.html")
                    html_content = report_data.get("html_content", "")

                    # 报告标题行，包含文本、详细内容按钮和下载按钮
                    col1, col2, col3 = st.columns([3, 1, 1])

                    with col1:
                        # 报告标题文本（纯文本，不是按钮）
                        st.markdown(f"**📄 {case_id}: {case_name}**")

                    with col2:
                        # 详细内容按钮
                        is_expanded = st.session_state.get(f"show_report_{case_id}", False)

                        if st.button(
                            "详细内容",
                            key=f"toggle_report_{case_id}",
                            use_container_width=True,
                            help="查看/隐藏详细报告内容"
                        ):
                            # 显示加载状态
                            with st.spinner("正在加载报告内容..."):
                                # 切换显示状态
                                st.session_state[f"show_report_{case_id}"] = not is_expanded
                                # 添加短暂延迟以显示加载状态
                                import time
                                time.sleep(0.5)
                            st.rerun()

                    with col3:
                        # 下载报告按钮
                        st.download_button(
                            label="📥 下载",
                            data=html_content,
                            file_name=filename,
                            mime="text/html",
                            key=f"download_report_{case_id}",
                            use_container_width=True
                        )

                    # 显示HTML报告内容（展开时）
                    if st.session_state.get(f"show_report_{case_id}", False):
                        # 使用streamlit的components显示HTML
                        st.markdown("---")
                        st.markdown(f"**📋 {case_id} 详细报告:**")

                        # 使用st.components.v1.html正常显示HTML内容
                        st.components.v1.html(
                            html_content,
                            height=600,
                            scrolling=True
                        )

                        st.markdown("---")

        # 显示会话文件目录信息
        session_dir = st.session_state.orchestrator.session_manager.get_session_dir(st.session_state.current_session_id)
        with st.expander("📁 会话文件目录"):
            st.write(f"**会话目录**: `{session_dir}`")
            st.write("**目录结构**:")
            st.write("- 📁 uploads/ - 上传的原始文件")
            st.write("- 📁 outputs/ - 生成的关系图等")
            st.write("- 📁 reports/ - HTML报告文件")
            st.write("- 📁 data/ - CSV数据和SQL文件")

# 这些函数已被新的多案件处理函数替代，保留用于向后兼容

def display_workflow_progress():
    """显示工作流进度"""
    if st.session_state.workflow_status:
        st.subheader("🔄 处理进度")
        
        for step in st.session_state.workflow_status:
            status_class = "success" if step["status"] == "success" else "error"
            timestamp = step.get("timestamp", "")[:19]
            
            st.markdown(f'''
            <div class="workflow-step {status_class}">
                <strong>{step["name"]}</strong> - {step["status"]}
                <br><small>时间: {timestamp}</small>
            </div>
            ''', unsafe_allow_html=True)

def main():
    """主函数"""
    init_session_state()

    # 显示侧边栏
    display_sidebar()

    # 主标题
    st.markdown('''
    <div class="main-header">
        <h1>📊 多案件信息提取分析助手</h1>
        <p>基于多智能体系统的批量案件自动化分析工具</p>
    </div>
    ''', unsafe_allow_html=True)

    # 显示工作流进度
    display_workflow_progress()

    # 主要工作流程
    if not st.session_state.current_batch_data:
        # 步骤1: 文件上传
        uploaded_file, user_requirements = display_file_upload_section()

        if uploaded_file is not None and not st.session_state.processing:
            # 开始处理文件
            st.session_state.processing = True
            st.session_state.file_processed = True

            try:
                # 保存上传的文件，保持原始文件名
                file_content = uploaded_file.getvalue()
                original_filename = uploaded_file.name

                saved_file_path = st.session_state.orchestrator.save_uploaded_file(
                    st.session_state.current_session_id, file_content, original_filename
                )

                st.info(f"📁 文件已保存到: {saved_file_path}")

                # 初始化进度跟踪
                st.session_state.processing_progress = {}

                # 创建进度显示容器
                progress_placeholder = st.empty()

                # 处理文件
                with st.spinner("🔄 正在处理多案件文件..."):
                    # 定义进度更新函数
                    async def progress_callback(case_index, status, message):
                        await update_progress(case_index, status, message)
                        # 更新进度显示
                        with progress_placeholder.container():
                            display_processing_progress()

                    result = asyncio.run(st.session_state.orchestrator.process_multi_case_file(
                        saved_file_path,
                        st.session_state.current_session_id,
                        user_requirements,
                        st.session_state.max_concurrent,
                        progress_callback
                    ))

                # 清除进度显示
                progress_placeholder.empty()

                # 更新工作流状态
                if result["status"] == "success":
                    st.session_state.workflow_status.append({
                        "name": "多案件批量处理",
                        "status": "success",
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    st.session_state.workflow_status.append({
                        "name": "多案件批量处理",
                        "status": "error",
                        "timestamp": datetime.now().isoformat()
                    })

                # 显示结果
                display_batch_results(result)

            except Exception as e:
                st.error(f"处理文件时发生错误: {str(e)}")
                st.session_state.workflow_status.append({
                    "name": "多案件批量处理",
                    "status": "error",
                    "timestamp": datetime.now().isoformat()
                })
            finally:
                st.session_state.processing = False
    else:
        # 显示已有的批量处理结果
        display_batch_results(st.session_state.current_batch_data)

    # 如果有批次数据，显示后续步骤
    if st.session_state.current_batch_data:
        # 显示人物关系图画廊
        display_relationship_gallery()

        # 显示数据库操作
        display_database_section()

        # 显示报告生成
        display_reports_section()

    # 页面底部信息
    st.markdown("---")
    st.markdown(f"""
    <div style="text-align: center; color: #666; padding: 20px;">
        🤖 多案件信息提取分析助手 | 支持批量案件处理、并发要素提取、关系图生成、数据库导入<br>
        <small>当前会话: {st.session_state.current_session_id} | 版本: 3.0 (多案件批量处理版)</small>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
