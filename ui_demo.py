#!/usr/bin/env python3
"""
UI改进演示脚本
"""

import streamlit as st

def main():
    st.set_page_config(
        page_title="UI改进演示",
        page_icon="🎨",
        layout="wide"
    )
    
    # 添加自定义CSS（与主应用相同的样式）
    st.markdown("""
    <style>
    /* 自定义按钮样式 - 蓝色主题 */
    .stButton > button {
        background-color: #1f77b4 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }
    
    .stButton > button:hover {
        background-color: #1565c0 !important;
        box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3) !important;
        transform: translateY(-1px) !important;
    }
    
    .stButton > button:active {
        background-color: #0d47a1 !important;
        transform: translateY(0px) !important;
    }
    
    .stDownloadButton > button {
        background-color: #1976d2 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
    }
    
    .stDownloadButton > button:hover {
        background-color: #1565c0 !important;
    }

    /* 报告按钮文字左对齐 */
    .stButton > button {
        text-align: left !important;
        justify-content: flex-start !important;
        padding-left: 12px !important;
        font-family: monospace !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.title("🎨 UI改进演示")
    st.write("展示新的蓝色按钮主题和HTML报告显示功能")
    
    # 演示按钮样式
    st.subheader("🔵 蓝色按钮主题")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("普通按钮", use_container_width=True):
            st.success("普通按钮被点击！")
    
    with col2:
        if st.button("主要按钮", type="primary", use_container_width=True):
            st.success("主要按钮被点击！")
    
    with col3:
        st.download_button(
            label="下载按钮",
            data="这是下载内容",
            file_name="demo.txt",
            mime="text/plain",
            use_container_width=True
        )
    
    with col4:
        if st.button("悬停效果", help="鼠标悬停查看效果", use_container_width=True):
            st.info("查看悬停时的阴影和位移效果！")
    
    st.markdown("---")
    
    # 演示HTML报告显示
    st.subheader("📋 HTML报告显示功能")
    
    # 模拟报告数据
    reports = {
        "A001": {
            "case_name": "走私香烟案",
            "html_content": """
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h1 style="color: #2c3e50;">A001:走私香烟案件分析报告</h1>
                <h2 style="color: #34495e;">📁 案件信息</h2>
                <p><strong>案件编号:</strong> A001</p>
                <p><strong>案件名称:</strong> 走私香烟案</p>
                <p><strong>承办单位:</strong> 深圳海关</p>
                
                <h2 style="color: #34495e;">👥 案件人员信息</h2>
                <table style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #3498db; color: white;">
                        <th style="border: 1px solid #ddd; padding: 8px;">姓名/代号</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">性别</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">角色</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">组织层级</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">张某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">男</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">主犯/组织者</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">核心层</td>
                    </tr>
                    <tr style="background-color: #f2f2f2;">
                        <td style="border: 1px solid #ddd; padding: 8px;">李某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">男</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">运输负责人</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">执行层</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">王某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">男</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">销售负责人</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">执行层</td>
                    </tr>
                </table>
                
                <h2 style="color: #34495e;">🔗 案件人员关系图</h2>
                <p style="text-align: center; color: #7f8c8d;">
                    [这里会显示人物关系图]
                </p>
                
                <div style="text-align: right; color: #7f8c8d; font-size: 0.9em; margin-top: 30px;">
                    报告生成时间: 2024-01-15 10:30:00
                </div>
            </div>
            """
        },
        "A002": {
            "case_name": "走私电子产品案",
            "html_content": """
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h1 style="color: #2c3e50;">A002:走私电子产品案件分析报告</h1>
                <h2 style="color: #34495e;">📁 案件信息</h2>
                <p><strong>案件编号:</strong> A002</p>
                <p><strong>案件名称:</strong> 走私电子产品案</p>
                <p><strong>承办单位:</strong> 广州海关</p>
                
                <h2 style="color: #34495e;">📊 分析过程</h2>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                    <p>经过深入分析，该案件涉及利用职务便利进行走私活动...</p>
                    <p>查明走私电子产品价值800万元，涉案资产已被查封。</p>
                </div>
                
                <h2 style="color: #34495e;">👥 案件人员信息</h2>
                <table style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #3498db; color: white;">
                        <th style="border: 1px solid #ddd; padding: 8px;">姓名/代号</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">角色</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">司法处置结果</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">赵某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">主犯</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">已被查获</td>
                    </tr>
                </table>
            </div>
            """
        }
    }
    
    # 显示报告
    for case_id, report_data in reports.items():
        case_name = report_data["case_name"]
        html_content = report_data["html_content"]

        # 报告标题行，包含展开/收起按钮和下载按钮
        col1, col2 = st.columns([4, 1])

        with col1:
            # 展开/收起按钮
            is_expanded = st.session_state.get(f"show_report_{case_id}", False)
            expand_icon = "‸" if is_expanded else "v"
            expand_text = "收起" if is_expanded else "展开"

            # 创建左对齐的按钮文本，图标放在右侧
            button_text = f"📄 {case_id}: {case_name} ({expand_text}详细报告)"
            # 计算需要的空格数来推送图标到右侧
            spaces = " " * max(0, 50 - len(button_text))
            full_button_text = f"{button_text}{spaces}{expand_icon}"

            if st.button(
                full_button_text,
                key=f"toggle_report_{case_id}",
                use_container_width=True
            ):
                # 切换显示状态
                st.session_state[f"show_report_{case_id}"] = not is_expanded
                st.rerun()

        with col2:
            # 下载报告按钮
            st.download_button(
                label="📥 下载",
                data=html_content,
                file_name=f"{case_id}.html",
                mime="text/html",
                key=f"download_report_{case_id}",
                use_container_width=True
            )

        # 显示HTML报告内容（展开时）
        if st.session_state.get(f"show_report_{case_id}", False):
            # 使用streamlit的components显示HTML
            st.markdown("---")
            st.markdown(f"**📋 {case_id} 详细报告:**")

            # 使用st.components.v1.html正常显示HTML内容
            st.components.v1.html(
                html_content,
                height=600,
                scrolling=True
            )

            st.markdown("---")

if __name__ == "__main__":
    main()
