#!/usr/bin/env python3
"""
多案件信息提取分析助手 - 快速启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """检查关键依赖"""
    required_packages = [
        'streamlit',
        'pandas', 
        'pymysql',
        'openpyxl',
        'matplotlib',
        'docker'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_files():
    """检查必要文件"""
    required_files = [
        'streamlit_app.py',
        'multi_agents.py',
        'agents.py'
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少以下文件: {', '.join(missing_files)}")
        return False
    
    return True

def test_imports():
    """测试关键模块导入"""
    try:
        from multi_agents import MultiCaseAnalysisOrchestrator
        print("✅ MultiCaseAnalysisOrchestrator导入成功")
        
        # 测试创建实例
        orchestrator = MultiCaseAnalysisOrchestrator()
        print("✅ 编排器实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def start_streamlit():
    """启动Streamlit应用"""
    try:
        print("\n🚀 启动多案件信息提取分析助手...")
        print("📊 功能特性:")
        print("   - 支持 xlsx/xls/csv 文件批量处理")
        print("   - 支持最大20个案件并发处理")
        print("   - 自动生成人物关系图和分析报告")
        print("   - 支持批量数据库导入")
        print("   - 访问地址: http://localhost:8502")
        print("\n按 Ctrl+C 停止服务")
        
        # 启动Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port=8502",
            "--server.address=0.0.0.0",
            "--server.headless=true",
            "--server.enableCORS=false",
            "--server.enableXsrfProtection=false",
            "--browser.gatherUsageStats=false"
        ]
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🔍 多案件信息提取分析助手 - 启动检查")
    print("="*50)
    
    print("\n📦 检查依赖包...")
    if not check_dependencies():
        return 1
    
    print("\n📁 检查文件...")
    if not check_files():
        return 1
    
    print("\n🧪 测试模块导入...")
    if not test_imports():
        return 1
    
    print("\n✅ 所有检查通过！")
    
    # 询问是否启动
    try:
        response = input("\n是否启动Web应用？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是', '']:
            start_streamlit()
        else:
            print("👋 已取消启动")
    except KeyboardInterrupt:
        print("\n👋 已取消启动")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
