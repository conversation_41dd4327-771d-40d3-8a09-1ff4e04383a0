graph TD
    A[陈某某] --> B(潘某某)
    A --> C(潘某)
    B --> D[潘林远]
    B --> E[何某]
    B --> F[何某某1]
    B --> G[劳某某]
    A --> H[红]
    H --> I[郑某某]
    A --> J[宏]
    J --> K[何某某2]
    J --> L[占某某]
    M[货车司机] --> N[李某]
    A --> M
    A --> O[蓝飞艇]
    A --> P[桂ERV186]
    B --> Q[桂E9K717]
    M --> R[桂KNH969]
    classDef 核心层 fill:#FFD700,stroke:#333;
    classDef 管理层 fill:#87CEEB,stroke:#333;
    classDef 执行层 fill:#90EE90,stroke:#333;
    classDef 组织 fill:#FFA07A,stroke:#333;
    classDef 工具 fill:#DDA0DD,stroke:#333;
    class A 核心层
    class B,C 核心层
    class D,E,F,G,H,J,M 核心层
    class H,J 组织
    class O,P,Q,R 工具