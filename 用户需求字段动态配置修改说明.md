# 用户需求字段动态配置修改说明

## 🎯 修改目标

1. **修改 user_requirements 默认填充**：改为具体的字段列表
2. **处理中文逗号**：将"，"替换为","并保存到 user_requirements_new
3. **统计字段数量**：保存到 user_requirements_count
4. **动态生成分析需求模板**：使用用户定义的字段
5. **修改 header**：使用动态字段替代固定字段

## ✅ 修改内容

### 一. streamlit_app.py 修改

#### 1. 用户需求字段输入区域

**修改前**:
```python
default_analysis_requirements = """任务目标：..."""
user_requirements = st.text_area(
    "",
    value=default_analysis_requirements,
    height=300,
    help="请输入具体的分析需求和任务目标..."
)
```

**修改后**:
```python
# 用户需求字段定义
user_requirements = st.text_area(
    "",
    value="实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,分工角色,关联人物,关联关系,关联工具/物品,关联行为,关联场所,司法处置结果,经济收益（元）",
    height=100,
    help="请输入需要提取的字段，用逗号分隔"
)

# 处理用户需求：替换中文逗号为英文逗号
user_requirements_new = user_requirements.replace("，", ",")

# 统计标签数量
user_requirements_count = len([field.strip() for field in user_requirements_new.split(",") if field.strip()])
```

#### 2. 动态生成分析需求模板

```python
analysis_requirements = f"""
任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{user_requirements_count}列）：
{user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。"""
```

#### 3. 传递分析需求而非用户需求

```python
if st.button("🚀 开始处理", type="primary", disabled=st.session_state.processing):
    return uploaded_file, analysis_requirements  # 传递生成的分析需求
```

### 二. multi_agents.py 修改

#### 1. 提取用户需求字段

在 `extract_multiple_cases` 方法中添加字段提取逻辑：

```python
# 从用户需求中提取字段列表
user_requirements_new = ""
if user_requirements:
    # 查找"输出CSV格式"部分
    import re
    csv_pattern = r'输出CSV格式.*?：\s*([^\n]+)'
    match = re.search(csv_pattern, user_requirements)
    if match:
        user_requirements_new = match.group(1).strip()
    else:
        # 如果没找到，使用默认字段
        user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,分工角色,关联人物,关联关系,关联工具/物品,关联行为,关联场所,司法处置结果,经济收益（元）"
```

#### 2. 动态生成 header

**修改前**:
```python
header = "批次号,承办单位,案件编号,案件名称,实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）"
```

**修改后**:
```python
header = f"批次号,承办单位,案件编号,案件名称,{user_requirements_new}"
```

## 🎨 用户界面改进

### 1. 字段输入区域
- **高度**: 从300px减少到100px，更适合字段列表输入
- **默认值**: 包含19个具体字段的完整列表
- **帮助提示**: 明确说明用逗号分隔字段

### 2. 信息显示
- **处理后字段**: 显示替换中文逗号后的字段列表
- **字段数量**: 实时显示字段统计
- **分析需求模板**: 可展开查看生成的完整分析需求

### 3. 模板展示
- 使用 `st.expander` 可折叠显示分析需求模板
- 模板内容动态生成，包含用户定义的字段
- 禁用编辑，仅供查看

## 🔧 技术实现

### 字段处理逻辑
```python
# 1. 替换中文逗号
user_requirements_new = user_requirements.replace("，", ",")

# 2. 统计字段数量
user_requirements_count = len([field.strip() for field in user_requirements_new.split(",") if field.strip()])

# 3. 动态生成模板
analysis_requirements = f"""..."""
```

### 正则表达式提取
```python
import re
csv_pattern = r'输出CSV格式.*?：\s*([^\n]+)'
match = re.search(csv_pattern, user_requirements)
```

## 📊 默认字段列表

新的默认字段包含19个字段：

1. 实体类型（人员/公司/组织）
2. 姓名/代号/公司/昵称
3. 性别
4. 年龄
5. 身份证号
6. 户籍地/现居地
7. 文化程度
8. 直接上级
9. 所属公司
10. 所属组织
11. 所属组织层级
12. 分工角色
13. 关联人物
14. 关联关系
15. 关联工具/物品
16. 关联行为
17. 关联场所
18. 司法处置结果
19. 经济收益（元）

## 🎯 功能特点

### 1. 动态配置
- 用户可以自定义需要提取的字段
- 系统自动统计字段数量
- 动态生成对应的分析需求模板

### 2. 智能处理
- 自动替换中文逗号为英文逗号
- 去除空白字段
- 正则表达式提取CSV格式字段

### 3. 用户友好
- 实时显示处理结果
- 可查看生成的分析需求模板
- 清晰的字段数量统计

## 🧪 测试验证

### 测试步骤
1. 运行应用：`streamlit run streamlit_app.py --server.port 8511`
2. 查看默认字段列表
3. 修改字段（可以添加、删除或修改字段）
4. 观察字段数量和处理后的字段显示
5. 展开查看生成的分析需求模板
6. 上传文件测试处理功能

### 预期结果
- ✅ 默认显示19个字段
- ✅ 中文逗号自动替换为英文逗号
- ✅ 实时显示字段数量
- ✅ 分析需求模板动态生成
- ✅ CSV header使用用户定义的字段

## 🎉 总结

通过这次修改，系统现在支持：

1. **灵活的字段配置** - 用户可以自定义提取字段
2. **智能的文本处理** - 自动处理中文逗号等格式问题
3. **动态的模板生成** - 根据用户字段生成对应的分析需求
4. **实时的反馈显示** - 用户可以看到处理结果和字段统计
5. **完整的数据流** - 从前端配置到后端处理的完整支持

这些改进大大提升了系统的灵活性和用户体验！
