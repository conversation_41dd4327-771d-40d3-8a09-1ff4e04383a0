#!/usr/bin/env python3
"""
测试前端显示修改
"""

import pandas as pd
import streamlit as st

def test_case_table_display():
    """测试案件列表表格显示"""
    print("🧪 测试案件列表表格显示...")
    
    # 模拟预处理后的案件数据
    processed_data = [
        {
            '案件编号': 'A001',
            '案件名称': '走私香烟案',
            '承办单位': '深圳海关',
            '案件内容': '经查，犯罪嫌疑人张某某组织走私香烟团伙，通过海上偷运方式走私香烟入境。该团伙分工明确，张某某负责组织协调，李某某负责运输，王某某负责销售。查明该团伙走私香烟价值500万元，现场查获走私香烟1000箱，账本若干。'
        },
        {
            '案件编号': 'A002',
            '案件名称': '走私电子产品案',
            '承办单位': '广州海关',
            '案件内容': '犯罪嫌疑人赵某某利用职务便利，走私进口电子产品，涉案金额巨大。查明走私电子产品价值800万元，查获走私电子产品，相关单证。涉案资产已被查封。'
        }
    ]
    
    # 构建案件列表数据表格
    case_table_data = []
    for case in processed_data:
        case_id = case.get('案件编号', 'N/A')
        case_name = case.get('案件名称', 'N/A')
        host_org = case.get('承办单位', 'N/A')
        case_content = case.get('案件内容', 'N/A')
        
        # 保持案件内容完整，不截断
        case_table_data.append({
            '案件编号': case_id,
            '案件名称': case_name,
            '承办单位': host_org,
            '案件内容': case_content
        })
    
    if case_table_data:
        case_df = pd.DataFrame(case_table_data)
        print("✅ 案件列表DataFrame创建成功")
        print(f"   数据形状: {case_df.shape}")
        print(f"   列名: {case_df.columns.tolist()}")
        
        # 检查案件内容是否完整
        for idx, row in case_df.iterrows():
            content_length = len(row['案件内容'])
            print(f"   案件 {row['案件编号']} 内容长度: {content_length} 字符")
            if content_length > 100:
                print(f"     ✅ 内容完整保留（超过100字符）")
            
        return True
    
    return False

def test_failed_case_table_display():
    """测试失败案件表格显示"""
    print("\n🧪 测试失败案件表格显示...")
    
    # 模拟失败案件数据
    failed_cases = [
        {
            'case_id': 'A003',
            'error': 'API调用超时，请检查网络连接或稍后重试。模型服务可能暂时不可用。'
        }
    ]
    
    # 模拟原始案件数据
    processed_data = [
        {
            '案件编号': 'A003',
            '案件名称': '走私奢侈品案',
            '承办单位': '珠海海关',
            '案件内容': '犯罪团伙以陈某某为首，专门从事奢侈品走私活动，手段隐蔽。查明奢侈品走私网络遍布多个城市，查获奢侈品，资金流水等证据。陈某某等人构成走私普通货物罪，案件移送检察院审查起诉。'
        }
    ]
    
    # 构建失败案件数据表格
    failed_table_data = []
    case_data_map = {case.get('案件编号', ''): case for case in processed_data}
    
    for failed_case in failed_cases:
        case_id = failed_case.get('case_id', 'N/A')
        error_msg = failed_case.get('error', '未知错误')
        
        # 从原始数据中获取案件信息
        original_case = case_data_map.get(case_id, {})
        case_name = original_case.get('案件名称', 'N/A')
        host_org = original_case.get('承办单位', 'N/A')
        case_content = original_case.get('案件内容', 'N/A')
        
        # 保持完整内容，不截断
        failed_table_data.append({
            '案件编号': case_id,
            '案件名称': case_name,
            '承办单位': host_org,
            '案件内容': case_content,
            '报错原因': error_msg
        })
    
    if failed_table_data:
        failed_df = pd.DataFrame(failed_table_data)
        print("✅ 失败案件DataFrame创建成功")
        print(f"   数据形状: {failed_df.shape}")
        print(f"   列名: {failed_df.columns.tolist()}")
        
        # 检查内容是否完整
        for idx, row in failed_df.iterrows():
            content_length = len(row['案件内容'])
            error_length = len(row['报错原因'])
            print(f"   案件 {row['案件编号']}:")
            print(f"     内容长度: {content_length} 字符")
            print(f"     错误信息长度: {error_length} 字符")
            if content_length > 100:
                print(f"     ✅ 案件内容完整保留")
            if error_length > 50:
                print(f"     ✅ 错误信息完整保留")
        
        return True
    
    return False

def main():
    """主测试函数"""
    print("🚀 开始前端显示修改测试...\n")
    
    tests = [
        ("案件列表表格显示", test_case_table_display),
        ("失败案件表格显示", test_failed_case_table_display)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print(f"\n{'='*50}")
    print("测试总结")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！前端显示修改成功。")
        print("\n📋 修改内容:")
        print("1. ✅ 预处理后的案件列表改为数据表格形式")
        print("2. ✅ 显示完整的案件内容（不截断）")
        print("3. ✅ 失败案件改为数据表格形式")
        print("4. ✅ 失败案件显示5个字段：案件编号、案件名称、承办单位、案件内容、报错原因")
        print("5. ✅ 设置表格高度支持滚动查看")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
