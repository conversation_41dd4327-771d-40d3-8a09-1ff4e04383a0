# 多案件信息提取分析助手

基于AutoGen框架的多智能体系统，支持批量处理案件数据文件，自动提取案件要素，生成人物关系图，并批量导入数据库。

## 🚀 功能特性

### 核心功能
- **多文件格式支持**: 支持 xlsx、xls、csv 格式的案件数据文件
- **智能数据预处理**: 自动去重、案件合并、字段标准化
- **并发案件处理**: 支持最大20个案件同时处理，可配置并发数
- **人物关系图生成**: 为每个案件自动生成Mermaid格式的人物关系图
- **批量数据库导入**: 一键批量导入到MySQL数据库
- **批量报告生成**: 为每个案件生成独立的HTML分析报告

### 技术特性
- **多智能体架构**: 基于AutoGen 0.5.7框架
- **实时进度显示**: Web界面实时显示处理进度
- **数据编辑支持**: 支持在线编辑提取的数据
- **会话管理**: 支持历史会话回溯
- **容错处理**: 支持部分案件处理失败的情况

## 📋 系统要求

### 环境依赖
- Python 3.8+
- Docker (用于Mermaid图表生成)
- MySQL 数据库

### Python包依赖
```bash
pip install streamlit pandas pymysql openpyxl matplotlib docker autogen-agentchat autogen-ext
```

## 🛠️ 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd AJagent
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置数据库
在 `multi_agents.py` 中修改数据库配置：
```python
self.config = {
    'host': '***********',
    'user': 'root', 
    'password': '123456',
    'database': 'mydb',
    'charset': 'utf8mb4'
}
```

### 4. 配置大模型
在 `multi_agents.py` 中修改模型配置：
```python
self.model_client = OpenAIChatCompletionClient(
    model="Qwen3-32B",
    base_url="http://*************:8011/v1",
    api_key="11888",
    # ...
)
```

### 5. 创建数据库表
```sql
CREATE TABLE mydb.ds_case_details (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '案件唯一ID',
    batch_id VARCHAR(50) NOT NULL COMMENT '数据批次号',
    host_org VARCHAR(100) COMMENT '承办单位',
    case_id VARCHAR(20) NOT NULL COMMENT '案件编号',
    case_name VARCHAR(255) COMMENT '案件名称',
    entity_type VARCHAR(50) COMMENT '实体类型',
    name_code VARCHAR(50) COMMENT '姓名/代号',
    gender VARCHAR(50) COMMENT '性别',
    age VARCHAR(50) COMMENT '年龄',
    id_card VARCHAR(50) COMMENT '身份证号',
    residence VARCHAR(255) COMMENT '户籍地/现居地',
    education VARCHAR(50) COMMENT '文化程度',
    direct_superior VARCHAR(100) COMMENT '直接上级',
    organization VARCHAR(100) COMMENT '所属组织',
    org_level VARCHAR(50) COMMENT '组织层级',
    role VARCHAR(100) COMMENT '分工角色',
    related_actions TEXT COMMENT '关联工具/行为',
    judicial_result TEXT COMMENT '司法处置结果',
    economic VARCHAR(50) COMMENT '经济收益（元）',
    data_time DATETIME NOT NULL COMMENT '数据插入时间',
    PRIMARY KEY (id),
    INDEX idx_case_name (case_name),
    INDEX idx_name_code (name_code),
    INDEX idx_id_card (id_card)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='案件详细信息表';
```

## 🚀 启动系统

### 方式1: 使用主程序启动
```bash
python main.py
```

### 方式2: 直接启动Streamlit
```bash
streamlit run streamlit_app.py --server.port=8502
```

### 方式3: 系统测试
```bash
python test_system.py
```

## 📖 使用指南

### 1. 准备数据文件
- 支持格式: `.xlsx`, `.xls`, `.csv`
- 必须包含字段: `案件编号`
- 建议包含字段: `案件名称`, `承办单位`, `正文内容`, `到案情况`, `依法侦查查明`, `犯罪证据`, `综上所述`, `其他说明`

### 2. 上传文件
1. 访问 http://localhost:8502
2. 在"步骤1"中上传案件数据文件
3. 输入分析需求（可选）
4. 点击"开始处理"

### 3. 监控处理进度
- 系统会实时显示每个案件的处理状态
- 支持最大并发数调整（1-20）
- 显示成功/失败案件统计

### 4. 查看处理结果
- **数据表格**: 查看和编辑提取的案件数据
- **关系图画廊**: 浏览每个案件的人物关系图
- **失败案件**: 查看处理失败的案件及错误信息

### 5. 数据库导入
- 点击"批量导入数据库"按钮
- 系统会将所有成功提取的数据导入数据库
- 每个会话只能导入一次

### 6. 生成报告
- 点击"批量生成HTML报告"
- 系统为每个案件生成独立的HTML报告
- 支持单个下载或批量ZIP下载

## 📁 文件结构

```
AJagent/
├── main.py                 # 主程序入口
├── streamlit_app.py        # Streamlit Web界面
├── agents.py              # 原有单案件处理模块（向后兼容）
├── multi_agents.py        # 新的多案件处理模块
├── test_system.py         # 系统测试脚本
├── README.md              # 说明文档
├── sessions/              # 会话数据目录
│   └── {session_id}/
│       ├── uploads/       # 上传文件
│       ├── outputs/       # 关系图等输出
│       ├── reports/       # HTML报告
│       └── data/          # CSV数据和日志
└── logs/                  # 系统日志
```

## 🔧 配置说明

### 并发处理配置
- 默认最大并发数: 10
- 可在Web界面调整: 1-20
- 建议根据系统性能和模型API限制调整

### 数据库配置
- 支持MySQL数据库
- 需要预先创建数据库和表结构
- 支持批量插入和事务回滚

### 模型配置
- 支持OpenAI兼容的API接口
- 需要配置base_url和api_key
- 建议使用支持function calling的模型

## 🐛 故障排除

### 常见问题

1. **Docker连接失败**
   - 确保Docker服务正在运行
   - 检查Docker权限设置
   - 关系图生成会自动降级到备用方案

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确保数据库服务可访问
   - 检查防火墙设置

3. **模型API调用失败**
   - 检查API地址和密钥
   - 确认模型服务可用
   - 检查网络连接

4. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件包含必要字段
   - 检查文件编码格式

### 日志查看
- 系统日志: `logs/case_analysis.log`
- 会话日志: `sessions/{session_id}/data/`
- 数据库操作日志: `sessions/{session_id}/data/batch_insert_log.json`

## 📞 技术支持

如有问题，请检查：
1. 系统日志文件
2. 数据库连接状态
3. 模型API可用性
4. Docker服务状态

## 📄 版本信息

- 版本: 3.0 (多案件批量处理版)
- 基于: AutoGen 0.5.7
- UI: Streamlit 1.45.1
- 更新日期: 2025-01-04
