#!/usr/bin/env python3
"""
最终UI改进演示
"""

import streamlit as st
import time

def main():
    st.set_page_config(
        page_title="UI改进演示",
        page_icon="✨",
        layout="wide"
    )
    
    st.title("✨ UI改进演示")
    st.write("展示最新的界面调整和用户体验改进")
    
    # 模拟新的界面布局
    st.markdown("## 🎯 新界面布局")
    
    # 1. 分析需求区域（优先显示）
    st.markdown("### 📋 分析需求")
    
    default_analysis_requirements = """任务目标：
1. 组织架构解析（主犯/从犯认定、分工逻辑）
2. 结构化数据提取（CSV格式，14列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的组织和所有提及到的人物信息：实体类型（人员或组织等），姓名/代号，性别，年龄，身份证号，户籍地/现居地，文化程度，直接上级，所属组织，组织层级，分工角色,关联工具/行为,司法处置结果,经济收益（元）

"年龄"要素提取要求：
其中"年龄" 要素，优先从案件内容中分析获取，若无法直接获得"年龄"要素，则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄" 

输出CSV格式（14列）：
实体类型,姓名/代号,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属组织,组织层级,分工角色,关联工具/行为,司法处置结果,经济收益（元）

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织之间的多层级关系，生成Mermaid格式的关系图代码。"""

    user_requirements = st.text_area(
        "",  # 移除小字标签
        value=default_analysis_requirements,
        height=300,
        help="请输入具体的分析需求和任务目标，可以根据实际需要修改分析步骤、输出格式等"
    )
    
    # 2. 文件上传区域（步骤1）
    st.markdown("---")
    st.markdown("### 📁 步骤1: 上传案件数据文件")
    uploaded_file = st.file_uploader(
        "",  # 移除重复标题
        type=['xlsx', 'xls', 'csv'],
        help="支持Excel文件(.xlsx/.xls)和CSV文件(.csv)，必须包含案件编号字段"
    )
    
    # 3. HTML报告演示区域
    st.markdown("---")
    st.markdown("### 📋 HTML报告演示")
    st.write("演示改进后的HTML报告加载体验")
    
    # 模拟报告数据
    reports = {
        "A001": {
            "case_name": "走私香烟案",
            "html_content": """
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h1 style="color: #2c3e50;">A001:走私香烟案件分析报告</h1>
                <h2 style="color: #34495e;">📁 案件信息</h2>
                <div style="background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p><strong>案件编号:</strong> A001</p>
                    <p><strong>案件名称:</strong> 走私香烟案</p>
                    <p><strong>承办单位:</strong> 深圳海关</p>
                </div>
                <h2 style="color: #34495e;">👥 案件人员信息</h2>
                <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                    <tr style="background-color: #3498db; color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px;">姓名/代号</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">角色</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">组织层级</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">张某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">主犯/组织者</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">核心层</td>
                    </tr>
                    <tr style="background-color: #f2f2f2;">
                        <td style="border: 1px solid #ddd; padding: 8px;">李某某</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">运输负责人</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">执行层</td>
                    </tr>
                </table>
            </div>
            """
        }
    }
    
    # 显示报告
    for case_id, report_data in reports.items():
        case_name = report_data["case_name"]
        html_content = report_data["html_content"]
        
        # 报告标题行
        col1, col2, col3 = st.columns([3, 1, 1])
        
        with col1:
            st.markdown(f"**📄 {case_id}: {case_name}**")
        
        with col2:
            # 详细内容按钮（带加载状态）
            is_expanded = st.session_state.get(f"demo_show_report_{case_id}", False)
            
            if st.button(
                "详细内容",
                key=f"demo_toggle_report_{case_id}",
                use_container_width=True,
                help="查看/隐藏详细报告内容"
            ):
                # 显示加载状态
                with st.spinner("正在加载报告内容..."):
                    # 切换显示状态
                    st.session_state[f"demo_show_report_{case_id}"] = not is_expanded
                    # 添加短暂延迟以显示加载状态
                    time.sleep(0.5)
                st.rerun()
        
        with col3:
            # 下载报告按钮
            st.download_button(
                label="📥 下载",
                data=html_content,
                file_name=f"{case_id}.html",
                mime="text/html",
                key=f"demo_download_report_{case_id}",
                use_container_width=True
            )
        
        # 显示HTML报告内容（展开时）
        if st.session_state.get(f"demo_show_report_{case_id}", False):
            st.markdown("---")
            st.markdown(f"**📋 {case_id} 详细报告:**")
            
            # 使用st.components.v1.html正常显示HTML内容
            st.components.v1.html(
                html_content,
                height=400,
                scrolling=True
            )
            
            st.markdown("---")
    
    # 改进说明
    st.markdown("---")
    st.markdown("## 📈 改进说明")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ✅ 界面调整")
        st.markdown("""
        **1. 显示顺序优化**
        - 先显示"📋 分析需求"
        - 再显示"📁 步骤1: 上传案件数据文件"
        - 符合逻辑：先配置需求，再上传文件
        
        **2. 标题简化**
        - "📋 分析需求配置" → "📋 分析需求"
        - 移除重复的小字标签
        - 界面更简洁清晰
        
        **3. 步骤标识**
        - 文件上传添加"步骤1"标识
        - 明确操作流程
        """)
    
    with col2:
        st.markdown("### ⚡ 性能优化")
        st.markdown("""
        **1. 加载状态改进**
        - 点击"详细内容"按钮时显示加载指示器
        - 使用 `st.spinner("正在加载报告内容...")`
        - 解决屏幕雾白问题
        
        **2. 用户反馈**
        - 明确的加载提示
        - 0.5秒延迟确保用户看到状态
        - 提升操作体验
        
        **3. 响应优化**
        - 减少用户等待时的困惑
        - 提供清晰的操作反馈
        """)
    
    # 技术实现
    st.markdown("---")
    st.markdown("## 🔧 技术实现")
    
    st.markdown("### 代码示例")
    
    # 显示顺序调整代码
    st.markdown("**1. 显示顺序调整:**")
    st.code("""
# 先显示分析需求
st.markdown("### 📋 分析需求")
user_requirements = st.text_area("", value=default_requirements, height=300)

# 再显示文件上传
st.markdown("---")
st.markdown("### 📁 步骤1: 上传案件数据文件")
uploaded_file = st.file_uploader("", type=['xlsx', 'xls', 'csv'])
""", language="python")
    
    # 加载状态改进代码
    st.markdown("**2. 加载状态改进:**")
    st.code("""
if st.button("详细内容", key=f"toggle_report_{case_id}"):
    # 显示加载状态
    with st.spinner("正在加载报告内容..."):
        # 切换显示状态
        st.session_state[f"show_report_{case_id}"] = not is_expanded
        # 添加短暂延迟以显示加载状态
        import time
        time.sleep(0.5)
    st.rerun()
""", language="python")
    
    # 总结
    st.markdown("---")
    st.markdown("## 🎯 改进总结")
    
    improvements = [
        "🔄 **逻辑顺序**: 先配置分析需求，再上传文件",
        "🎨 **界面简洁**: 移除重复标题和标签",
        "⚡ **加载反馈**: 明确的加载状态指示器",
        "📱 **用户体验**: 更流畅的操作流程",
        "🎯 **视觉层次**: 清晰的功能区域划分",
        "♿ **可访问性**: 更好的信息组织和反馈"
    ]
    
    for improvement in improvements:
        st.markdown(f"- {improvement}")

if __name__ == "__main__":
    main()
