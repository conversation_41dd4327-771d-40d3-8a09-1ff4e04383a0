#!/usr/bin/env python3
"""
测试JSON序列化修复
"""

import pandas as pd
import json
from datetime import datetime
from multi_agents import CustomJSONEncoder, SessionManager, ExcelFileProcessor

def test_json_serialization():
    """测试JSON序列化功能"""
    print("🧪 测试JSON序列化修复...")
    
    # 创建测试数据
    test_data = {
        '案件编号': ['TEST001', 'TEST002'],
        '案件名称': ['测试案件1', '测试案件2'],
        '承办单位': ['测试单位1', '测试单位2'],
        '案件内容': ['测试内容1', '测试内容2']
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建包含DataFrame的复杂对象
    complex_data = {
        "status": "success",
        "dataframe": df,
        "timestamp": datetime.now(),
        "processed_data": df.to_dict('records'),
        "file_info": {
            "name": "test.xlsx",
            "size": 1024
        }
    }
    
    try:
        # 测试自定义JSON编码器
        json_str = json.dumps(complex_data, cls=CustomJSONEncoder, indent=2)
        print("✅ 自定义JSON编码器测试通过")
        
        # 测试反序列化
        decoded_data = json.loads(json_str)
        print("✅ JSON反序列化测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON序列化测试失败: {e}")
        return False

def test_session_manager():
    """测试SessionManager"""
    print("\n🧪 测试SessionManager...")
    
    try:
        session_manager = SessionManager()
        
        # 创建测试数据
        test_data = {
            "batch_id": "test_batch",
            "file_info": {"name": "test.xlsx"},
            "preprocessing": {
                "status": "success",
                "processed_data": [
                    {"案件编号": "TEST001", "案件名称": "测试案件1"},
                    {"案件编号": "TEST002", "案件名称": "测试案件2"}
                ]
            },
            "timestamp": datetime.now()
        }
        
        # 测试保存
        session_id = "test_session"
        session_manager.save_session_data(session_id, test_data)
        print("✅ 会话数据保存测试通过")
        
        # 测试加载
        loaded_data = session_manager.load_session_data(session_id)
        print("✅ 会话数据加载测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ SessionManager测试失败: {e}")
        return False

def test_file_processing():
    """测试文件处理"""
    print("\n🧪 测试文件处理...")
    
    try:
        # 创建测试Excel文件
        test_data = {
            '案件编号': ['A001', 'A001', 'A002'],
            '案件名称': ['案件1', '案件1', '案件2'],
            '承办单位': ['单位1', '单位1', '单位2'],
            '正文内容': ['内容1', '内容2', '内容3'],
            '数据版本号': [1, 2, 1]
        }
        
        df = pd.DataFrame(test_data)
        test_file = "test_cases_fix.xlsx"
        df.to_excel(test_file, index=False)
        
        # 测试文件读取
        df_read, file_info = ExcelFileProcessor.read_excel_file(test_file)
        print("✅ 文件读取测试通过")
        
        # 测试数据预处理
        preprocess_result = ExcelFileProcessor.preprocess_case_data(df_read)
        
        if preprocess_result["status"] == "success":
            print("✅ 数据预处理测试通过")
            print(f"   处理结果: {preprocess_result['processing_summary']}")
            
            # 检查processed_data是否为可序列化的格式
            processed_data = preprocess_result["processed_data"]
            json.dumps(processed_data, cls=CustomJSONEncoder)
            print("✅ 预处理结果JSON序列化测试通过")
            
            return True
        else:
            print(f"❌ 数据预处理失败: {preprocess_result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 文件处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始JSON序列化修复测试...\n")
    
    tests = [
        ("JSON序列化", test_json_serialization),
        ("SessionManager", test_session_manager),
        ("文件处理", test_file_processing)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print(f"\n{'='*50}")
    print("测试总结")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！JSON序列化问题已修复。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
